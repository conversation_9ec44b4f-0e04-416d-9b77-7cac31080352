'use client'
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card } from '@/components/_common/ui'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import Image from 'next/image'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { toast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'

/**
 * KONFIGURASI BACKEND YANG DIPERLUKAN:
 * - Payment gateway redirect URL harus diubah dari:
 *   {baseUrl}/detail-psychologist/[id]/konseling/booking-status
 *   menjadi:
 *   {baseUrl}/transaksi/booking-status
 *
 * - Success URL: {baseUrl}/transaksi/booking-status?status=success&transaction_id={id}
 * - Failed URL: {baseUrl}/transaksi/booking-status?status=failed&transaction_id={id}
 * - Cancel URL: {baseUrl}/transaksi/booking-status?status=cancel&transaction_id={id}
 */

function BookingStatusPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [paymentStatus, setPaymentStatus] = useState('failed')
  const [counselingData, setCounselingData] = useState<any>(null)
  const [transactionId, setTransactionId] = useState('')

  // Fetch booking data from localStorage or query params
  useEffect(() => {
    // Try to get transaction ID from URL query params
    const urlParams = new URLSearchParams(window.location.search)
    const txnId = urlParams.get('transaction_id')
    if (txnId) {
      setTransactionId(txnId)
    }

    // Get previous booking data
    const savedBooking = localStorage.getItem('konselingBooking')
    if (savedBooking) {
      try {
        const bookingData = JSON.parse(savedBooking)
        setCounselingData(bookingData)
      } catch (error) {
        console.error('Error parsing saved booking', error)
      }
    }

    // Set payment status based on URL params
    const status = urlParams.get('status')
    if (status === 'success' || status === 'paid') {
      setPaymentStatus('success')
    } else if (status === 'failed' || status === 'cancel') {
      setPaymentStatus('failed')
    } else {
      // Default to success if no status specified (for testing)
      setPaymentStatus('success')
    }

    setIsLoading(false)
  }, [])

  // Render success status page
  const renderSuccessStatus = () => {
    return (
      <div className="w-full min-h-screen bg-gray-50">
        {/* Mobile Layout */}
        <div className="block lg:hidden">
          <div className="min-h-screen bg-gray-50 flex flex-col">
            {/* Blue background section */}
            <div className="bg-main-100 rounded-b-md px-4 sm:px-6 pt-32 pb-32 items-center justify-center">
              <div className="text-white text-center max-w-sm mx-auto">
                <h1 className="text-lg sm:text-xl md:text-2xl font-semibold mb-3 sm:mb-4 leading-tight">
                  Terima kasih atas kepercayaannya
                </h1>
                <p className="text-sm sm:text-base opacity-90 leading-relaxed">
                  Semoga kita dapat menjadi versi terbaik dari diri kita
                </p>
              </div>
            </div>

            {/* Card floating over blue background */}
            <div className="relative px-4 sm:px-6 -mt-24 sm:-mt-32">
              <Card className="rounded-lg sm:rounded-xl overflow-hidden bg-white shadow-lg">
                <div className="bg-green-50 text-green-600 text-sm sm:text-base font-medium p-3 sm:p-4 border-b border-green-100">
                  Sudah Dibayar
                </div>
                <div className="p-4 sm:p-5 md:p-6">
                  <p className="text-gray-500 text-xs sm:text-sm mb-1 sm:mb-2">Jadwal Konselingmu</p>
                  <p className="font-semibold text-sm sm:text-base md:text-lg mb-2 sm:mb-3 leading-tight">
                    {counselingData?.formattedDate || 'Jumat, 4 Agu 2023, 18:00-19:00 WIB'}
                  </p>
                  <p className="text-gray-500 text-xs sm:text-sm mb-4 sm:mb-5">
                    {counselingData?.method === 'Call' ? 'Voice Call' : 'Video Call'} - Google Meet
                  </p>

                  <div className="flex items-center gap-3 sm:gap-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                      <Image
                        width={48}
                        height={48}
                        src={counselingData?.psychologistImage || '/placeholder-avatar.jpg'}
                        alt={counselingData?.psychologistName || 'Mahira Syafana'}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-sm sm:text-base leading-tight">
                        {counselingData?.psychologistName || 'name undifined'}
                      </h3>
                      <p className="text-gray-500 text-xs sm:text-sm mt-1 leading-relaxed">
                        {counselingData?.specializations || 'specializations undifined'}
                      </p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Button section with white background */}
            <div className="bg-gray-50 px-4 sm:px-6 pt-6 sm:pt-8 pb-8 sm:pb-10 flex-shrink-0">
              <ButtonPrimary
                variant="contained"
                className="w-full py-3 sm:py-4 text-sm sm:text-base font-medium"
                onClick={() => {
                  router.push('/transaksi')
                }}
              >
                Lihat Detail Konseling
              </ButtonPrimary>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <div className="w-full max-w-6xl mx-auto">
              <div className="relative bg-main-100 rounded-2xl overflow-hidden">
                <div className="flex items-center min-h-[400px]">
                  {/* Left side - Text */}
                  <div className="w-1/2 px-8 py-12 text-white">
                    <h1 className="text-3xl xl:text-4xl font-semibold mb-4">
                      Terima kasih atas kepercayaannya
                    </h1>
                    <p className="text-lg xl:text-xl opacity-90">
                      Semoga kita dapat menjadi versi terbaik dari diri kita
                    </p>
                  </div>

                  {/* Right side - Card */}
                  <div className="w-1/2 px-8 py-12 flex justify-center">
                    <div className="w-full max-w-sm">
                      <Card className="rounded-lg overflow-hidden bg-white shadow-lg">
                        <div className="bg-green-50 text-green-600 font-medium p-3 border-b border-green-100">
                          Sudah Dibayar
                        </div>
                        <div className="p-6">
                          <p className="text-gray-500 text-sm mb-1">Jadwal Konselingmu</p>
                          <p className="font-semibold text-lg mb-2">
                            {counselingData?.formattedDate || 'Jumat, 4 Agu 2023, 18:00-19:00 WIB'}
                          </p>
                          <p className="text-gray-500 text-sm mb-6">
                            {counselingData?.method === 'Call' ? 'Voice Call' : 'Video Call'} - Google Meet
                          </p>

                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                              <Image
                                width={48}
                                height={48}
                                src={counselingData?.psychologistImage || '/placeholder-avatar.jpg'}
                                alt={counselingData?.psychologistName || 'Mahira Syafana'}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-base">
                                {counselingData?.psychologistName || 'Mahira Syafana M.Psi. Psikolog'}
                              </h3>
                              <p className="text-gray-500 text-sm mt-1">
                                {counselingData?.specializations ||
                                  'Percintaan, Keluarga, Kecemasan, Organisasi, +4 lainnya'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </Card>
                    </div>
                  </div>
                </div>
              </div>

              {/* Button outside the blue background */}
              <div className="flex justify-center mt-8">
                <ButtonPrimary
                  variant="contained"
                  className="px-8 py-3"
                  onClick={() => {
                    router.push('/transaksi')

                    // if (transactionId) {
                    //   const status =
                    //     paymentStatus === 'success'
                    //       ? 'Menunggu-konfirmasi-psikolog'
                    //       : 'Pembayaran-konseling-gagal'

                    //   if (paymentStatus === 'success') {
                    //     localStorage.removeItem('konselingBooking')
                    //     localStorage.removeItem('currentCounselingId')
                    //   }

                    //   router.push(`/transaksi/${status}/${transactionId}`)
                    // } else {
                    //   router.push('/transaksi')
                    // }
                  }}
                >
                  Lihat Detail Jadwal
                </ButtonPrimary>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Render failed status display
  const renderFailedStatus = () => {
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 py-8">
        <div className="w-full max-w-sm sm:max-w-md">
          <Card className="p-6 sm:p-8 md:p-10 rounded-lg sm:rounded-xl shadow-lg bg-white">
            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-red-100 flex items-center justify-center mb-4 sm:mb-6">
                <SVGIcons name={IIcons.Close} className="text-red-500 h-6 w-6 sm:h-8 sm:w-8" />
              </div>
              <h2 className="text-lg sm:text-xl font-semibold mb-2 leading-tight">Pembayaran Gagal</h2>
              <p className="text-gray-500 text-sm sm:text-base mb-6 leading-relaxed">
                Maaf, pembayaran Anda tidak dapat diproses
              </p>

              {transactionId && (
                <div className="mb-6 w-full p-3 sm:p-4 bg-gray-50 rounded-lg">
                  <p className="text-xs sm:text-sm text-gray-500 mb-1">ID Transaksi</p>
                  <p className="font-medium break-all text-sm sm:text-base">{transactionId}</p>
                </div>
              )}

              <ButtonPrimary
                variant="outlined"
                className="px-6 py-2 sm:py-3 w-full sm:w-auto text-sm sm:text-base font-medium"
                onClick={() => router.push('/transaksi')}
              >
                Kembali ke Transaksi
              </ButtonPrimary>
            </div>
          </Card>
        </div>
      </div>
    )
  }

  const renderStatusContent = () => {
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 py-8">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-lg text-main-100 sm:text-xl font-semibold mb-2 leading-tight">
              Memuat status pembayaran...
            </h2>
            <p className="text-gray-500 text-sm sm:text-base leading-relaxed">Mohon tunggu sebentar</p>
          </div>
        </div>
      )
    }

    if (paymentStatus === 'success') {
      return renderSuccessStatus()
    } else {
      return renderFailedStatus()
    }
  }

  return renderStatusContent()
}

export default BookingStatusPage
