'use client'
import { Card } from '@/components/_common/ui'
import { Swiper, SwiperSlide, useSwiper } from 'swiper/react'
import { Pagination, FreeMode, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import ArrowRightIcon from '@/assets/icons/arrow-right.svg'
import { useEffect, useRef, useState } from 'react'
import { PsychologistCardItem } from './PsychologistCardItem'
import { MobilePsychologistCardItem } from './MobilePsychologistCardItem'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { psychologistService } from '@/services/psychologist.service'
import { useRouter } from 'next/navigation'
import { LoadingCard, LoadingCardWithAvatar } from '@/components/loading/LoadingCard'

export const OurPsychologist = () => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [slideContent, setSlideContent] = useState<any>([])
  const refPrev = useRef<any>(null)
  const refNext = useRef<any>(null)

  useEffect(() => {
    setIsLoading(true)
    psychologistService
      .getPsychologist()
      .then((response) => {
        if (response.data?.length) {
          setSlideContent(response.data)
          // Filter psychologists to only include those with available schedules
          const filteredPsychologists = response.data.filter((psychologist: any) => {
            // Check if the psychologist has any available schedules
            return (
              psychologist.breakdownAvailability &&
              psychologist.breakdownAvailability.length > 0 &&
              psychologist.breakdownAvailability[0].schedule &&
              psychologist.breakdownAvailability[0].schedule.some((day: any) =>
                day.times.some((slot: any) => slot.isAvailable)
              )
            )
          })
          // setSlideContent(filteredPsychologists)
        }
        setIsLoading(false)
      })
      .catch((error) => {
        setIsLoading(false)
        console.log(error)
      })
  }, [])

  function SlideNextButton() {
    const swiper = useSwiper()
    return (
      <span
        ref={refNext}
        className="rounded-full border border-line-200 p-1 cursor-pointer"
        onClick={() => swiper.slideNext()}
      >
        <ArrowRightIcon className="w-6 h-6" />
      </span>
    )
  }
  function SlidePrevButton() {
    const swiper = useSwiper()
    return (
      <span
        ref={refPrev}
        className="rounded-full border border-line-200 p-1 cursor-pointer"
        onClick={() => swiper.slidePrev()}
      >
        <ArrowLeftIcon className="w-6 h-6" />
      </span>
    )
  }
  return (
    <div className="w-full md:max-w-screen xl:max-w-[1120px] relative flex flex-col gap-y-2">
      <div className="flex justify-between px-4 xl:px-0">
        <span className="hidden md:block text-[38px] font-bold text-[#222222]">Psikolog Terbaik Kami</span>
        <span className="flex md:hidden text-subheading-md font-bold">Psikolog Kami</span>
        <div className="flex gap-x-2 items-center mb-2 md:mb-0">
          <div className="hidden md:flex gap-x-2">
            <span
              className="rounded-full border border-line-200 p-1 cursor-pointer w-[34px] h-[34px] flex items-center justify-center"
              onClick={() => refPrev.current?.click()}
            >
              <ArrowLeftIcon className="w-6 h-6" />
            </span>
            <span
              className="rounded-full border border-line-200 p-1 cursor-pointer w-[34px] h-[34px] flex items-center justify-center"
              onClick={() => refNext.current?.click()}
            >
              <ArrowRightIcon className="w-6 h-6" />
            </span>
          </div>
          <span
            className="text-main-100 font-bold cursor-pointer"
            onClick={() => router.push('/search-psikolog')}
          >
            Lihat semua
          </span>
        </div>
      </div>
      <div className="relative w-full hidden md:flex">
        {isLoading ? (
          <div className="flex gap-6 w-full px-4">
            <LoadingCard />
            <LoadingCard />
            <LoadingCard />
          </div>
        ) : (
          <div className="w-full">
            <Swiper
              slidesPerView={'auto'}
              // navigation
              spaceBetween={24}
              freeMode={true}
              pagination={{
                clickable: true,
              }}
              modules={[FreeMode, Navigation]}
              className="mySwiperpsychologist"
            >
              <div className="hidden">
                <SlideNextButton />
                <SlidePrevButton />
              </div>
              {slideContent.length > 0
                ? slideContent.map((slide: any) => (
                    <SwiperSlide
                      className="first:pl-4 last:pr-4 xl:first:pl-0 xl:last:pr-0"
                      key={slide.id}
                      style={{ width: 'fit-content' }}
                    >
                      <div className="max-w-[262px] h-full">
                        <PsychologistCardItem {...slide} />
                      </div>
                    </SwiperSlide>
                  ))
                : null}
            </Swiper>
          </div>
        )}
      </div>

      {/* MOBILE VIEW */}
      <div className="grid grid-cols-1 md:hidden gap-y-4 mb-6 px-4">
        {isLoading ? (
          <div className="flex flex-col gap-6 w-full px-4 z-1">
            <LoadingCardWithAvatar />
            <LoadingCardWithAvatar />
            <LoadingCardWithAvatar />
          </div>
        ) : (
          <>
            {slideContent.length > 0
              ? slideContent
                  .slice(0, 3)
                  .map((slide: any, id: number) => <MobilePsychologistCardItem key={slide.id} {...slide} />)
              : null}
            {slideContent.length > 0 && (
              <ButtonPrimary
                className="rounded-sm bg-white z-1"
                variant="outlined"
                size="sm"
                onClick={() => router.push('/search-psikolog')}
              >
                Lihat Semua Psikolog
              </ButtonPrimary>
            )}
          </>
        )}
      </div>
    </div>
  )
}
