'use client'
import { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/_common/ui'
import { useBooking } from '@/context/useBookingCounseling'
import { useSelector } from '@/store'
import { toast } from '@/components/ui/use-toast'
import { counsellingService } from '@/services/counselling.service'

import { useProblemCategories } from '../../hook/useGetProblemCategory'
import AppointmentDetails from './_components/AppointmentDetail'
import PriceBreakdown from './_components/PriceBreakDown'
import NotesModal from './_components/NotesModal'
import VoucherModal from './_components/VoucherModal'

// Types
interface NotesData {
  categories: string[]
  expectations: string
  feelings: string
  description: string
}

// Forecast response interface
interface ForecastResponse {
  id: string
  forecast: {
    amount: number
    discountAmount: number
    platformFee: number
    [key: string]: any
  }
}

// Payment response interface
interface PaymentResponse {
  createdCounseling: {
    id: string
    [key: string]: any
  }
  payment: {
    paymentUrl: string
  }
}

// Add the forecast service interface
interface CounselingForecastPayload {
  psychologistId: string
  problemCategory: string[]
  method: 'Call' | 'VideoCall'
  location: 'Online' | 'Offline'
  schedule: string
  complaint: string
  expectation: string
  description: string
  duration: number
  voucherId?: string
}

// Available voucher interface with display properties
interface AvailableVoucher {
  id: string
  title: string
  description: string
  discountValue: number
  discountType: 'FIXED' | 'PERCENTAGE'
  expiryDate: string
}

function KonselingPage() {
  const { bookingState } = useBooking()
  const router = useRouter()
  const { user } = useSelector((state) => state.Authentication)

  const [isNotesModalOpen, setIsNotesModalOpen] = useState(false)
  const [isVoucherModalOpen, setIsVoucherModalOpen] = useState(false)
  const [notesData, setNotesData] = useState<NotesData>({
    categories: [],
    expectations: '',
    feelings: '',
    description: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [selectedVoucher, setSelectedVoucher] = useState<AvailableVoucher | null>(null)
  const [voucherApplied, setVoucherApplied] = useState(false)
  const [availableVouchers, setAvailableVouchers] = useState<AvailableVoucher[]>([])
  const [voucherCode, setVoucherCode] = useState('')
  const [voucherError, setVoucherError] = useState('')

  const { categories: problemCategories, loading: loadingCategories } = useProblemCategories()

  // Default booking data
  const defaultBookingData = {
    psychologistId: 'default',
    psychologistName: 'Dr. Psikolog',
    specializations: 'specializations',
    formattedDate: 'Senin, 24 April 2025 - 14:00 WIB',
    rawSchedule: '2025-04-24T07:00:00.000Z',
    method: 'VideoCall' as const,
    location: 'Online' as const,
    duration: 60,
    price: 149000,
    discount: 0,
    voucherPromo: 0,
    totalAfterDiscount: 149000,
    finalPrice: 149000,
    voucherId: '',
  }

  // Safe localStorage getter
  const getFromLocalStorage = (key: string) => {
    if (typeof window !== 'undefined') {
      return window.localStorage.getItem(key)
    }
    return null
  }

  // Safe localStorage setter
  const setToLocalStorage = (key: string, value: string) => {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem(key, value)
    }
  }

  // Safe localStorage remover
  const removeFromLocalStorage = (key: string) => {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem(key)
    }
  }

  // Load booking data from state or localStorage
  const displayData = useMemo(() => {
    if (!user) {
      router.push('/auth/login')
      return defaultBookingData
    }

    if (bookingState) return bookingState

    const savedBooking = getFromLocalStorage('konselingBooking')
    if (savedBooking) {
      try {
        return JSON.parse(savedBooking)
      } catch (error) {
        console.error('Error parsing saved booking', error)
        return defaultBookingData
      }
    }

    return defaultBookingData
  }, [bookingState, user, router])

  // Problem categories with their respective icons
  const categoryOptions = useMemo(() => {
    return problemCategories.map((category) => ({
      id: category.id,
      name: category.problemCategory,
      icon: category.iconUrl || 'Add', // Fallback to Add icon if no iconUrl
    }))
  }, [problemCategories])

  // Get psychologist ID for storage key
  const psychologistId = useMemo(() => {
    return displayData.psychologistId || displayData.psychologistName.replace(/\s+/g, '_').toLowerCase()
  }, [displayData])

  // Generate storage key specific to this psychologist
  const getNotesStorageKey = () => {
    return `konselingNotes_${psychologistId}`
  }

  // Calculate if all notes are filled
  const hasFilledNotes = useMemo(() => {
    return (
      notesData.categories.length > 0 &&
      notesData.expectations.trim().length > 0 &&
      notesData.feelings.trim().length > 0
    )
  }, [notesData])

  // Store notes data in localStorage when it changes
  useEffect(() => {
    // Only save to localStorage if there's actual data
    if (
      notesData.categories.length > 0 ||
      notesData.expectations.trim() !== '' ||
      notesData.feelings.trim() !== ''
    ) {
      setToLocalStorage(getNotesStorageKey(), JSON.stringify(notesData))
    }
  }, [notesData, psychologistId])

  // Load notes data from localStorage on initial render or when psychologist changes
  useEffect(() => {
    const savedNotes = getFromLocalStorage(getNotesStorageKey())

    if (savedNotes) {
      try {
        const parsedNotes = JSON.parse(savedNotes) as NotesData
        setNotesData(parsedNotes)
      } catch (error) {
        console.error('Error parsing saved notes', error)
        // Reset notes data if there's an error
        setNotesData({
          categories: [],
          expectations: '',
          feelings: '',
          description: '',
        })
      }
    } else {
      // Reset notes data when switching to a psychologist with no saved notes
      setNotesData({
        categories: [],
        expectations: '',
        feelings: '',
        description: '',
      })
    }
  }, [psychologistId])

  // Handle booking state
  useEffect(() => {
    if (!bookingState) {
      // Load booking data from localStorage if available
      const savedBooking = getFromLocalStorage('konselingBooking')
      if (!savedBooking) {
        console.log('No booking state found, would normally redirect')
        // Uncomment to enable actual redirect:
        // router.back()
      }
    } else {
      // Save booking state to localStorage
      setToLocalStorage('konselingBooking', JSON.stringify(bookingState))
    }
  }, [bookingState, router])

  // Load available vouchers
  useEffect(() => {
    fetchAvailableVouchers()

    // Check if there's a voucher already applied
    const savedBooking = getFromLocalStorage('konselingBooking')
    if (savedBooking) {
      try {
        const parsedBooking = JSON.parse(savedBooking)
        if (parsedBooking.voucherId && parsedBooking.voucherPromo > 0) {
          setVoucherApplied(true)
        }
      } catch (error) {
        console.error('Error parsing saved booking for voucher check', error)
      }
    }
  }, [])

  // Map category IDs to actual names for the API payload
  const getCategoryNamesForApi = (): string[] => {
    return notesData.categories
      .map((id) => categoryOptions.find((cat) => cat.id === id)?.name)
      .filter(Boolean) as string[]
  }

  // Fetch list of available vouchers
  const fetchAvailableVouchers = async () => {
    try {
      setIsLoading(true)
      const response = await counsellingService.getListVoucherAvailable()
      if (response && Array.isArray(response)) {
        // Format vouchers for display
        const formattedVouchers: AvailableVoucher[] = response.map((voucher) => {
          // Format expiry date
          const expiryDate = new Date(voucher.validUntil).toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          })

          // Create descriptive title based on discount type
          const title =
            voucher.discountType === 'FIXED'
              ? `Diskon Rp${voucher.discountValue.toLocaleString('id-ID')}`
              : `Diskon ${voucher.discountValue}%`

          // Create description
          const description =
            voucher.discountType === 'FIXED'
              ? `Potongan tetap Rp${voucher.discountValue.toLocaleString('id-ID')}`
              : `Potongan ${voucher.discountValue}% (maks. Rp${voucher.maxDiscountAmount.toLocaleString('id-ID')})`

          return {
            id: voucher.id,
            title,
            description,
            discountValue: voucher.discountValue,
            discountType: voucher.discountType,
            expiryDate,
          }
        })

        setAvailableVouchers(formattedVouchers)

        // If there's a voucher ID in the booking data, find and select it
        if (displayData.voucherId) {
          const matchingVoucher = formattedVouchers.find((v) => v.id === displayData.voucherId)
          if (matchingVoucher) {
            setSelectedVoucher(matchingVoucher)
            setVoucherCode(matchingVoucher.id)
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch available vouchers:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Apply voucher code entered by user
  const handleApplyVoucher = async () => {
    if (!voucherCode.trim()) {
      setVoucherError('Silakan masukkan kode voucher')
      return
    }

    try {
      setIsLoading(true)
      setVoucherError('')

      const response = await counsellingService.getCounselingVoucherByIdUser(voucherCode.trim())

      if (response) {
        // Format voucher data
        const expiryDate = new Date(response.validUntil).toLocaleDateString('id-ID', {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
        })

        const newVoucher: AvailableVoucher = {
          id: response.id,
          title:
            response.discountType === 'FIXED'
              ? `Diskon Rp${response.discountValue.toLocaleString('id-ID')}`
              : `Diskon ${response.discountValue}%`,
          description:
            response.discountType === 'FIXED'
              ? `Potongan tetap Rp${response.discountValue.toLocaleString('id-ID')}`
              : `Potongan ${response.discountValue}% (maks. Rp${response.maxDiscountAmount.toLocaleString('id-ID')})`,
          discountValue: response.discountValue,
          discountType: response.discountType,
          expiryDate,
        }

        // Check voucher validity
        const now = new Date()
        const validFrom = new Date(response.validFrom)
        const validUntil = new Date(response.validUntil)

        if (now < validFrom) {
          setVoucherError('Voucher belum dapat digunakan')
          return
        }

        if (now > validUntil) {
          setVoucherError('Voucher sudah kedaluwarsa')
          return
        }

        if (response.usageCount >= response.maxUsage) {
          setVoucherError('Voucher sudah habis digunakan')
          return
        }

        if (displayData.price < response.minTransactionValue) {
          setVoucherError(`Minimal transaksi Rp${response.minTransactionValue.toLocaleString('id-ID')}`)
          return
        }

        // Set the voucher and preselect it
        setSelectedVoucher(newVoucher)

        toast({
          title: 'Berhasil',
          variant: 'success',
          description: 'Voucher ditemukan dan dapat digunakan',
        })
      } else {
        setVoucherError('Voucher tidak ditemukan atau tidak valid')
      }
    } catch (error) {
      console.error('Error checking voucher:', error)
      setVoucherError('Voucher tidak ditemukan atau tidak valid')
    } finally {
      setIsLoading(false)
    }
  }

  // Select voucher from available list
  const handleSelectVoucher = (voucher: AvailableVoucher) => {
    setSelectedVoucher(voucher)
    setVoucherCode(voucher.id)
    setVoucherError('')
  }

  // Apply the selected voucher
  const handleApplySelectedVoucher = () => {
    if (!selectedVoucher) {
      toast({
        title: 'Error',
        description: 'Silakan pilih voucher terlebih dahulu',
        variant: 'destructive',
      })
      return
    }

    // Calculate discount amount
    let discountAmount = 0
    if (selectedVoucher.discountType === 'FIXED') {
      discountAmount = selectedVoucher.discountValue
    } else {
      // For percentage type, calculate the discount
      discountAmount = Math.round((displayData.price * selectedVoucher.discountValue) / 100)
    }

    // Update booking data with new voucher
    const totalAfterDiscount = Math.max(0, displayData.price - displayData.discount - discountAmount)
    const updatedBookingData = {
      ...displayData,
      voucherPromo: discountAmount,
      totalAfterDiscount,
      finalPrice: totalAfterDiscount,
      voucherId: selectedVoucher.id,
    }

    setToLocalStorage('konselingBooking', JSON.stringify(updatedBookingData))

    // Show success message
    toast({
      title: 'Berhasil',
      description: `Voucher ${selectedVoucher.id} berhasil digunakan!`,
    })

    // Close modal and update state
    setIsVoucherModalOpen(false)
    setVoucherApplied(true)

    // Force re-render with the latest data
    window.location.href = window.location.href
  }

  // Handle voucher removal
  const handleRemoveVoucher = () => {
    // Update booking data by removing voucher
    const updatedBookingData = {
      ...displayData,
      voucherPromo: 0,
      totalAfterDiscount: displayData.price - displayData.discount,
      finalPrice: displayData.price - displayData.discount,
      voucherId: '',
    }

    setToLocalStorage('konselingBooking', JSON.stringify(updatedBookingData))

    // Update state
    setVoucherApplied(false)
    setSelectedVoucher(null)
    setVoucherCode('')

    toast({
      title: 'Berhasil',
      description: 'Voucher telah dihapus',
    })

    // Refresh the page to reflect changes
    window.location.href = window.location.href
  }

  // Handle form input changes
  const handleInputChange = (field: keyof NotesData, value: string | string[]) => {
    setNotesData({
      ...notesData,
      [field]: value,
    })
  }

  // Toggle category selection
  const toggleCategory = (categoryId: string) => {
    const updatedCategories = notesData.categories.includes(categoryId)
      ? notesData.categories.filter((id) => id !== categoryId)
      : [...notesData.categories, categoryId]

    handleInputChange('categories', updatedCategories)
  }

  // Handle notes changes
  const handleSaveNotes = () => {
    if (notesData.categories.length === 0) {
      toast({
        title: 'Error',
        description: 'Silakan pilih minimal satu kategori masalah',
        variant: 'destructive',
      })
      return
    }

    if (notesData.expectations.trim() === '') {
      toast({
        title: 'Error',
        description: 'Silakan isi harapan setelah konseling',
        variant: 'destructive',
      })
      return
    }

    if (notesData.feelings.trim() === '') {
      toast({
        title: 'Error',
        description: 'Silakan ceritakan apa yang kamu rasakan',
        variant: 'destructive',
      })
      return
    }

    toast({
      title: 'Berhasil',
      variant: 'success',
      description: 'Catatan konseling berhasil disimpan',
    })
    setIsNotesModalOpen(false)
  }

  // Reset forecast ID when notes are cleared
  const handleClearNotes = () => {
    const confirmed = window.confirm('Apakah Anda yakin ingin menghapus semua catatan?')
    if (confirmed) {
      setNotesData({
        categories: [],
        expectations: '',
        feelings: '',
        description: '',
      })

      removeFromLocalStorage(getNotesStorageKey())

      toast({
        title: 'Berhasil',
        variant: 'success',
        description: 'Catatan konseling berhasil dihapus',
      })
    }
  }

  // Create forecast and proceed to payment
  const handlePayment = async () => {
    if (!hasFilledNotes) {
      setIsNotesModalOpen(true)
      return
    }

    try {
      setIsLoading(true)

      // Step 1: Create forecast
      // Ensure psychologistId and duration are correctly formatted for the API payload
      const forecastPayload: CounselingForecastPayload = {
        psychologistId: displayData.psychologistId,
        problemCategory: getCategoryNamesForApi(),
        method: displayData.method,
        location: displayData.location || 'Online', // Default to Online if not specified
        schedule: displayData.rawSchedule, // Use the raw ISO date string
        complaint: notesData.feelings,
        expectation: notesData.expectations,
        description: notesData.description || '',
        duration: Number(displayData.duration), // Ensure it's a number
      }

      // Add voucher ID if available
      if (displayData.voucherId) {
        forecastPayload.voucherId = displayData.voucherId
      }

      console.log('Sending forecast payload:', forecastPayload)

      // Call the forecast API
      const forecastResponse = (await counsellingService.postCounselingForecast(
        forecastPayload
      )) as ForecastResponse

      console.log('Raw forecast response:', forecastResponse)

      if (!forecastResponse || !forecastResponse.id) {
        throw new Error('No forecast ID received from server')
      }

      // Step 2: Create counseling session with forecast ID
      const payload = {
        forecastId: forecastResponse.id,
      }

      console.log('Creating counseling with payload:', payload)

      // Call the API to create counseling session using forecast ID
      const paymentResponse = (await counsellingService.counselingControllerCreateByForecastId(
        payload
      )) as PaymentResponse

      console.log('Counseling creation response:', paymentResponse)

      if (paymentResponse && paymentResponse.payment && paymentResponse.payment.paymentUrl) {
        // Store the counseling ID if needed for later reference
        if (paymentResponse.createdCounseling && paymentResponse.createdCounseling.id) {
          setToLocalStorage('currentCounselingId', paymentResponse.createdCounseling.id)
        }

        // Redirect to payment URL
        window.location.href = paymentResponse.payment.paymentUrl
      } else {
        throw new Error('No payment URL received from server')
      }
    } catch (error) {
      console.error('Error in payment process:', error)
      toast({
        title: 'Gagal',
        description: 'Proses pembayaran gagal. Silakan coba lagi.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Get selected category names for display
  const getSelectedCategoryNames = (): string => {
    return notesData.categories
      .map((id) => categoryOptions.find((cat) => cat.id === id)?.name)
      .filter(Boolean)
      .join(', ')
  }

  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6 mt-6">Ringkasan konseling</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left side - Appointment details */}
        <AppointmentDetails
          displayData={displayData}
          hasFilledNotes={hasFilledNotes}
          getSelectedCategoryNames={getSelectedCategoryNames}
          openNotesModal={() => setIsNotesModalOpen(true)}
        />

        {/* Right side - Price breakdown */}
        <PriceBreakdown
          displayData={displayData}
          voucherApplied={voucherApplied}
          isLoading={isLoading}
          openVoucherModal={() => setIsVoucherModalOpen(true)}
          onRemoveVoucher={handleRemoveVoucher}
          onClickPayment={handlePayment}
        />
      </div>

      {/* Notes Modal */}
      <NotesModal
        isOpen={isNotesModalOpen}
        onClose={() => setIsNotesModalOpen(false)}
        notesData={notesData}
        categoryOptions={categoryOptions}
        loadingCategories={loadingCategories}
        onInputChange={handleInputChange}
        toggleCategory={toggleCategory}
        onSave={handleSaveNotes}
        onClear={handleClearNotes}
      />

      {/* Voucher Modal */}
      <VoucherModal
        isOpen={isVoucherModalOpen}
        onClose={() => setIsVoucherModalOpen(false)}
        voucherCode={voucherCode}
        setVoucherCode={setVoucherCode}
        voucherError={voucherError}
        selectedVoucher={selectedVoucher}
        availableVouchers={availableVouchers}
        isLoading={isLoading}
        isLoadingVouchers={isLoading}
        displayData={displayData}
        onApplyVoucher={handleApplyVoucher}
        onSelectVoucher={handleSelectVoucher}
        onApplySelectedVoucher={handleApplySelectedVoucher}
        onClearSelectedVoucher={() => {
          setSelectedVoucher(null)
          setVoucherCode('')
        }}
      />
    </div>
  )
}

export default KonselingPage
