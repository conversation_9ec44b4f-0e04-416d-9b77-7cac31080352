import { Card } from '@/components/_common/ui'
import Image from 'next/image'
import { SpecializationList } from './SpecializationList'
import WorkIcon from '@/assets/icons/work.svg'
import Link from 'next/link'
import { ScheduleList } from './ScheduleList'

type TimeSlot = {
  time: string
  dateTimeWithTimezone: string
  isAvailable: boolean
}

type ScheduleDay = {
  date: string
  timezone: string
  times: TimeSlot[]
}

type Availability = {
  durationInMinute: number
  price: number
  schedule: ScheduleDay[]
}

export type ProblemCategory = {
  problemCategory: string
  iconUrl?: string | null
  order: number
  createdAt: string
  modifiedAt: string
}

export type PsychologistProps = {
  id: string
  profilePhoto: string
  fullName: string
  calculatedExperience: string
  problemCategory: ProblemCategory[]
  breakdownAvailability?: Availability[]
}

export const PsychologistCardItem = (psychologist: PsychologistP<PERSON>) => {
  const specialization = psychologist?.problemCategory?.length
    ? psychologist.problemCategory.map((item) => item.problemCategory)
    : []

  // Function to convert experience years to range
  const getExperienceRange = (experience: string | number) => {
    const years = Number(experience) || 0
    
    if (years < 2) {
      return '0-2 tahun'
    } else if (years >= 2 && years < 4) {
      return '2-4 tahun'
    } else if (years >= 4 && years <= 10) {
      return '4-10 tahun'
    } else {
      return '>10 tahun'
    }
  }
  return (
    <Card className="p-2 xs:p-2 sm:p-2 md:p-[8px] min-h-full bg-white flex flex-col gap-y-4">
      {/* Foto */}
      <div className="relative h-[200px] w-full rounded-xl overflow-hidden bg-blue-50">
        <Link href={`/detail-psychologist/${psychologist?.id}`}>
          <Image
            src={psychologist?.profilePhoto ?? '/images/default-psychologist.png'}
            alt="Foto Psikolog"
            fill
            className="object-cover object-center"
          />
        </Link>
      </div>
      {/* Konten */}
      <div className="flex flex-col gap-2 overflow-hidden">
        {/* Nama */}
        <Link href={`/detail-psychologist/${psychologist?.id}`}>
          <span className="text-body-lg font-bold text-gray-900">{psychologist?.fullName}</span>
        </Link>
        {/* Spesialisasi */}
        <SpecializationList
          className="text-body-sm text-gray-500 break-words"
          list={specialization}
          showItem={3}
        />
        {/* Pengalaman */}
        <span className="flex items-center gap-x-1 text-gray-400 text-body-sm font-medium">
          <WorkIcon />
          {getExperienceRange(psychologist?.calculatedExperience)}
        </span>
        <ScheduleList
          breakdownAvailability={psychologist.breakdownAvailability}
          maxItems={20}
          psychologistId={psychologist.id}
        />
      </div>
    </Card>
  )
}
