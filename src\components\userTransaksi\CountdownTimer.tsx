'use client'
import { useEffect, useState } from 'react'

interface CountdownTimerProps {
  createdAt: string
  durationMinutes?: number
}

export default function CountdownTimer({ createdAt, durationMinutes = 30 }: CountdownTimerProps) {
  const [remainingTime, setRemainingTime] = useState<number>(() => {
    const expireTime = new Date(new Date(createdAt).getTime() + durationMinutes * 60000)
    return expireTime.getTime() - new Date().getTime()
  })

  useEffect(() => {
    const interval = setInterval(() => {
      setRemainingTime((prev) => {
        if (prev <= 1000) {
          clearInterval(interval)
          return 0
        }
        return prev - 1000
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  if (remainingTime <= 0) {
    return <span className="text-red-500 text-sm">Waktu Habis</span>
  }

  const hours = Math.floor(remainingTime / (1000 * 60 * 60))
  const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000)

  return (
    <span className="text-sm text-[#E42B3B]">
      Sisa Waktu{' '}
      {`${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`}
    </span>
  )
}
