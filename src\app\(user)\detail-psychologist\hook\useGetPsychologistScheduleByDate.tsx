import { psychologistService } from '@/services/psychologist.service'
import { useState, useEffect } from 'react'

interface BreakdownAvailability {
  durationInMinute: number
  price: number
  schedule?: ScheduleItem[]
}

interface ScheduleItem {
  date: string
  timezone: string
  times: TimeSlot[]
}

interface TimeSlot {
  time: string
  dateTimeWithTimezone: string
  isAvailable: boolean
}

interface PsychologistScheduleData {
  breakdownAvailability?: BreakdownAvailability[]
}

export const useGetPsychologistScheduleByDate = (psychologistId: string, selectedDate?: string) => {
  const [scheduleData, setScheduleData] = useState<PsychologistScheduleData | null>(null)
  const [loadingSchedule, setLoadingSchedule] = useState(false)
  const [errorSchedule, setErrorSchedule] = useState(null)

  const fetchScheduleByDate = async (date: string) => {
    try {
      setLoadingSchedule(true)
      const response = await psychologistService.getPsychologistScheduleByDate(psychologistId, date)
      setScheduleData(response)
      setErrorSchedule(null)
    } catch (err) {
      setErrorSchedule(err as null)
      setScheduleData(null)
    } finally {
      setLoadingSchedule(false)
    }
  }

  useEffect(() => {
    if (psychologistId && selectedDate) {
      fetchScheduleByDate(selectedDate)
    }
  }, [psychologistId, selectedDate])

  return {
    scheduleData,
    loadingSchedule,
    errorSchedule,
    refetch: fetchScheduleByDate,
  }
}
