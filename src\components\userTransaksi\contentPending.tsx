'use client'
import { CounselingData } from '@/hooks/useCounselings'
import CardTransaksi from './cardTransaksi'
import { PaymentStatus } from '@/constans/StaticOptions'
import { formatDate, formatTime } from '../../utils/utilsDate'
import CountdownTimer from './CountdownTimer'

interface ContentPendingProps {
  title: string
  title2: string
  title3: string
  pendingData: CounselingData[]
  loading: boolean
}

export default function ContentPending({ title, title2, title3, pendingData, loading }: ContentPendingProps) {
  if (loading) {
    return <div className="flex justify-center my-8">loading..</div>
  }

  if (pendingData.length === 0) {
    return (
      <div className="text-center my-8 px-4">
        <p className="text-gray-500">Tidak ada jadwal konseling yang menunggu konfirmasi atau pembayaran.</p>
      </div>
    )
  }

  const psychologistConfirmationData = pendingData.filter(
    (item) => item.status === PaymentStatus.PAID_PAYMENT
  )

  const clientConfirmationData = pendingData.filter((item) =>
    [PaymentStatus.RESCHEDULE_BY_CLIENT, PaymentStatus.RESCHEDULE_BY_PSYCHOLOGIST].includes(
      item.status as PaymentStatus
    )
  )

  const pendingPaymentData = pendingData.filter((item) =>
    item.status === PaymentStatus.PENDING_PAYMENT
  )
  
  return (
    <div className="flex flex-col gap-4 mt-3 px-4 md:px-0">
      {/* Waiting for psychologist confirmation */}
      {psychologistConfirmationData.length > 0 && (
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">{title}</h3>
          {psychologistConfirmationData.map((item) => {
            const startDate = new Date(item.startTime)
            const formattedDate = formatDate(startDate)
            const formattedStartTime = formatTime(new Date(item.startTime))
            const formattedEndTime = formatTime(new Date(item.endTime))

            return (
              <CardTransaksi
                key={item.id}
                id={item.id}
                title={title}
                buttonType=""
                buttonTitle=""
                buttonTitle2=""
                textButtonColor=""
                numberButton="0"
                date={formattedDate}
                time={`${formattedStartTime} - ${formattedEndTime} WIB`}
                method={item.method}
                psychologistName={item.psychologist.fullName}
                psychologistPhoto={item.psychologist.profilePhoto}
              />
            )
          })}
        </div>
      )}

      {/* Waiting for client confirmation */}
      {clientConfirmationData.length > 0 && (
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">{title2}</h3>
          {clientConfirmationData.map((item) => {
            const startDate = new Date(item.startTime)
            const formattedDate = formatDate(startDate)
            const formattedStartTime = formatTime(new Date(item.startTime))
            const formattedEndTime = formatTime(new Date(item.endTime))

            return (
              <CardTransaksi
                key={item.id}
                id={item.id}
                title={title2}
                buttonType=""
                buttonTitle="Terima"
                buttonTitle2="Ubah"
                textButtonColor="text-[#222222]"
                numberButton="2"
                date={formattedDate}
                time={`${formattedStartTime} - ${formattedEndTime} WIB`}
                method={item.method}
                psychologistName={item.psychologist.fullName}
                psychologistPhoto={item.psychologist.profilePhoto}
              />
            )
          })}
        </div>
      )}

      {/* Waiting for payment */}
      {pendingPaymentData.length > 0 && (
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">{title3}</h3>
          {pendingPaymentData.map((item) => {
            const startDate = new Date(item.startTime)
            const formattedDate = formatDate(startDate)
            const formattedStartTime = formatTime(new Date(item.startTime))
            const formattedEndTime = formatTime(new Date(item.endTime))

            return (
              <CardTransaksi
                key={item.id}
                id={item.id}
                title={title3}
                buttonType=""
                buttonTitle="Bayar Konseling"
                buttonTitle2={<CountdownTimer createdAt={item.payment.createdAt} />}
                textButtonColor="text-[#E42B3B]"
                numberButton="2"
                date={formattedDate}
                time={`${formattedStartTime} - ${formattedEndTime} WIB`}
                method={item.method}
                psychologistName={item.psychologist.fullName}
                psychologistPhoto={item.psychologist.profilePhoto}
                paymentUrl={item.payment.gatewayUrl}
              />
            )
          })}
        </div>
      )}
    </div>
  )
}
