'use client'
import React from 'react'
import { AppBigText, AppBigCaption, Card } from '@/components/_common/ui'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { Separator } from '@/components/ui/separator'

interface AppointmentDetailsProps {
  displayData: {
    formattedDate: string
    method: string
    psychologistImage?: string
    psychologistName: string
    specializations: string
  }
  hasFilledNotes: boolean
  getSelectedCategoryNames: () => string
  openNotesModal: () => void
}

const AppointmentDetails: React.FC<AppointmentDetailsProps> = ({
  displayData,
  hasFilledNotes,
  getSelectedCategoryNames,
  openNotesModal,
}) => {
  return (
  <Card className="p-3 xs:p-3 sm:p-3 md:p-3 bg-white flex flex-col justify-center gap-y-2 w-full md:h-[300px] md:overflow-hidden">
      {/* Section 1: <PERSON><PERSON><PERSON>ling */}
      <div className="p-3">
        <div>
          <AppBigCaption className="text-gray-500">Jadwal Konselingmu</AppBigCaption>
        </div>
        <div>
          <AppBigText bold className="text-lg">{displayData.formattedDate}</AppBigText>
        </div>
        <div>
          <AppBigCaption className="text-gray-500">{displayData.method === 'Call' ? 'Call' : 'VideoCall'} - Google Meet</AppBigCaption>
        </div>
      </div>

      {/* Separator */}
      <div className="-mx-3">
        <Separator orientation="horizontal" />
      </div>

      {/* Section 2: Nama dan Foto Psikolog */}
      <div className="flex items-center gap-x-4 p-3">
        <AvatarWithInfo
          className="w-16 h-16"
          image={displayData.psychologistImage ?? ''}
          heading={<AppBigText bold>{displayData.psychologistName}</AppBigText>}
          subHeading={
            <AppBigCaption className="text-gray-500">
              {displayData.specializations}
            </AppBigCaption>
          }
        />
      </div>

      {/* Separator */}
      <div className="-mx-3">
        <Separator orientation="horizontal" />
      </div>

      {/* Section 3: Button Isi/Edit Catatan Konseling */}
      <div className="-mx-3 px-3 py-2 rounded-b-lg" style={{ backgroundColor: '#F5FCFF' }}>
        {hasFilledNotes ? (
          <div
            className="flex items-center justify-between text-white p-2 cursor-pointer hover:bg-opacity-90"
            onClick={openNotesModal}
          >
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-lg bg-white bg-opacity-30 flex items-center justify-center mr-3">
                {/* Filled note icon (check inside rounded square) */}
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <circle cx="16.0026" cy="16" r="10.6667" fill="white" />
                  <path d="M12.6641 16L15.2247 18.5606C15.2832 18.6192 15.3782 18.6192 15.4368 18.5606L20.6641 13.3333" stroke="#039EE9" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <div>
                <AppBigText bold className="text-white">Catatan Konseling</AppBigText>
                <AppBigCaption className="text-white opacity-80">Kategori: {getSelectedCategoryNames()}</AppBigCaption>
              </div>
            </div>
            <SVGIcons name={IIcons.ArrowRight} className="text-white" />
          </div>
        ) : (
          <div
            className="flex items-center text-black p-2 cursor-pointer hover:bg-opacity-90"
            onClick={openNotesModal}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-3">
              <path d="M8.5 12.5L15.5 12.5" stroke="#039EE9" strokeLinecap="round"/>
              <path d="M8.5 15.5L12.5 15.5" stroke="#039EE9" strokeLinecap="round"/>
              <path d="M5.5 5.9C5.5 5.05992 5.5 4.63988 5.66349 4.31901C5.8073 4.03677 6.03677 3.8073 6.31901 3.66349C6.63988 3.5 7.05992 3.5 7.9 3.5H12.5059C12.8728 3.5 13.0562 3.5 13.2288 3.54145C13.3819 3.57819 13.5282 3.6388 13.6624 3.72104C13.8138 3.8138 13.9435 3.94352 14.2029 4.20294L17.7971 7.79706C18.0565 8.05648 18.1862 8.1862 18.279 8.33757C18.3612 8.47178 18.4218 8.6181 18.4586 8.77115C18.5 8.94378 18.5 9.12723 18.5 9.49411V18.1C18.5 18.9401 18.5 19.3601 18.3365 19.681C18.1927 19.9632 17.9632 20.1927 17.681 20.3365C17.3601 20.5 16.9401 20.5 16.1 20.5H7.9C7.05992 20.5 6.63988 20.5 6.31901 20.3365C6.03677 20.1927 5.8073 19.9632 5.66349 19.681C5.5 19.3601 5.5 18.9401 5.5 18.1V5.9Z" stroke="#039EE9"/>
              <path d="M12.5 3.5V7.1C12.5 7.94008 12.5 8.36012 12.6635 8.68099C12.8073 8.96323 13.0368 9.1927 13.319 9.33651C13.6399 9.5 14.0599 9.5 14.9 9.5H18.5" stroke="#039EE9"/>
            </svg>
            <AppBigText className="flex-1 text-black">Isi Catatan Konseling</AppBigText>
            <SVGIcons name={IIcons.ArrowRight} className="text-black" />
          </div>
        )}
      </div>
    </Card>
  )
}

export default AppointmentDetails
