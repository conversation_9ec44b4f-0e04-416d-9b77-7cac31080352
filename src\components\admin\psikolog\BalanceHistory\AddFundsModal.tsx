import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { AppModal } from '@/components/_common/Modal/AppModal'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { Calendar } from '@/components/ui/calendar'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import AppInput from '@/components/_common/input/Input'
import { psychologistService } from '@/services/psychologist.service'
import { useToast } from '@/components/ui/use-toast'

interface AddFundsModalProps {
  isOpen: boolean
  onClose: () => void
  psychologistId: string
}

interface AddFundsFormValues {
  type: 'EXTRA_TIME' | 'OTHERS'
  duration: string
  amount: string
  transferDate: Date
  description: string
}

export function AddFundsModal({ isOpen, onClose, psychologistId }: AddFundsModalProps) {
  const { toast } = useToast()
  const [selectedTime, setSelectedTime] = useState({
    hours: '00',
    minutes: '00',
  })
  const [showDatePicker, setShowDatePicker] = useState(false)

  const {
    register,
    setValue,
    getValues,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<AddFundsFormValues>({
    defaultValues: {
      type: 'EXTRA_TIME',
      duration: '15',
      amount: '',
      transferDate: undefined,
      description: '',
    },
  })

  const selectedType = watch('type')

  const formatDateTime = (date?: Date) => {
    if (!date) return ''
    return new Date(date).toLocaleString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const onSubmit = async (data: AddFundsFormValues) => {
    try {
      if (!data.transferDate || isNaN(new Date(data.transferDate).getTime())) {
        throw new Error('Tanggal transfer tidak valid.')
      }

      const { hours, minutes } = selectedTime

      if (isNaN(Number(hours)) || isNaN(Number(minutes))) {
        throw new Error('Jam transfer tidak lengkap.')
      }

      const transferedDate = new Date(data.transferDate)
      transferedDate.setHours(Number(hours))
      transferedDate.setMinutes(Number(minutes))

      const payload = {
        ...data,
        date: transferedDate.toISOString(),
      }

      await psychologistService.postPsychologistTopup(payload, psychologistId)
      onClose()
    } catch (error) {
      console.error('Error adding funds:', error)
      toast({
        variant: 'danger',
        title: 'Terjadi kesalahan saat menyimpan data, silahkan ulangi beberapa saat lagi.',
      })
    }
  }

  return (
    <AppModal open={isOpen} onClose={onClose} title="Tambah Dana" className="w-full max-w-[700px]">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 py-4">
        <div className="space-y-4">
          <label className="text-base font-medium">Jenis Penambahan</label>
          <RadioGroup
            defaultValue="EXTRA_TIME"
            {...register('type')}
            onValueChange={(value) => setValue('type', value as 'EXTRA_TIME' | 'OTHERS')}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="EXTRA_TIME" id="extra-time" />
              <label htmlFor="extra-time">Extra time</label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="OTHERS" id="others" />
              <label htmlFor="others">Lainnya (Honor seminar, webinar, dsb)</label>
            </div>
          </RadioGroup>
        </div>

        {selectedType === 'EXTRA_TIME' ? (
          <div className="space-y-4">
            <label className="text-base font-medium">Jumlah Penambahan</label>
            <RadioGroup
              defaultValue="15"
              {...register('duration')}
              onValueChange={(value) => setValue('duration', value)}
            >
              {['15', '30', '45', '60'].map((duration) => (
                <div key={duration} className="flex items-center space-x-2">
                  <RadioGroupItem value={duration} id={`duration-${duration}`} />
                  <label htmlFor={`duration-${duration}`}>{duration} menit</label>
                </div>
              ))}
            </RadioGroup>
          </div>
        ) : (
          <AppInput
            {...register('amount', { required: 'Nominal wajib diisi' })}
            label="Harga (Rp)*"
            type="text"
            placeholder="Rp2.000.000"
            errorMsg={errors.amount?.message}
          />
        )}

        <div className="space-y-2">
          <label className="text-base font-medium">Tanggal dan Waktu</label>
          <div
            className="w-full rounded-lg border p-3 cursor-pointer hover:bg-gray-50"
            onClick={() => setShowDatePicker(true)}
          >
            {formatDateTime(getValues('transferDate')) || 'Pilih tanggal dan waktu'}
          </div>
        </div>

        <AppModal
          open={showDatePicker}
          onClose={() => setShowDatePicker(false)}
          title="Pilih Tanggal dan Waktu"
          className="w-full max-w-[360px]"
        >
          <div className="space-y-4 p-4">
            <Calendar
              mode="single"
              selected={getValues('transferDate')}
              onSelect={(date) => {
                if (date) {
                  const currentDate = new Date(date)
                  currentDate.setHours(parseInt(selectedTime.hours))
                  currentDate.setMinutes(parseInt(selectedTime.minutes))
                  setValue('transferDate', currentDate, { shouldValidate: true })
                }
              }}
              className="w-full"
              captionLayout="dropdown-buttons"
              fromYear={2023}
              toYear={new Date().getFullYear()}
            />

            <div className="flex items-end gap-2 pt-4 border-t">
              <div className="flex-1 space-y-2">
                <label className="text-sm font-medium">Jam</label>
                <input
                  type="number"
                  min="0"
                  max="23"
                  value={selectedTime.hours}
                  onChange={(e) => {
                    const value = e.target.value
                    const hours = value === '' ? '00' : value.padStart(2, '0')
                    setSelectedTime((prev) => ({
                      ...prev,
                      hours: Math.min(23, Math.max(0, parseInt(hours)))
                        .toString()
                        .padStart(2, '0'),
                    }))

                    if (getValues('transferDate')) {
                      const currentDate = new Date(getValues('transferDate'))
                      currentDate.setHours(parseInt(hours))
                      setValue('transferDate', currentDate, { shouldValidate: true })
                    }
                  }}
                  className="w-full rounded-lg border p-2"
                  placeholder="00"
                />
              </div>
              <span className="mb-2">:</span>
              <div className="flex-1 space-y-2">
                <label className="text-sm font-medium">Menit</label>
                <input
                  type="number"
                  min="0"
                  max="59"
                  value={selectedTime.minutes}
                  onChange={(e) => {
                    const value = e.target.value
                    const minutes = value === '' ? '00' : value.padStart(2, '0')
                    setSelectedTime((prev) => ({
                      ...prev,
                      minutes: Math.min(59, Math.max(0, parseInt(minutes)))
                        .toString()
                        .padStart(2, '0'),
                    }))

                    if (getValues('transferDate')) {
                      const currentDate = new Date(getValues('transferDate'))
                      currentDate.setMinutes(parseInt(minutes))
                      setValue('transferDate', currentDate, { shouldValidate: true })
                    }
                  }}
                  className="w-full rounded-lg border p-2"
                  placeholder="00"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-4">
              <ButtonPrimary
                variant="outlined"
                color="gray"
                onClick={() => setShowDatePicker(false)}
                size="sm"
              >
                Batal
              </ButtonPrimary>
              <ButtonPrimary variant="contained" onClick={() => setShowDatePicker(false)} size="sm">
                Pilih
              </ButtonPrimary>
            </div>
          </div>
        </AppModal>

        <div className="space-y-2">
          <textarea
            {...register('description')}
            placeholder="Masukkan keterangan tambahan..."
            className="w-full min-h-[100px] rounded-lg border p-2"
          />
        </div>

        <div className="flex justify-end space-x-4">
          <ButtonPrimary variant="outlined" color="gray" onClick={onClose} type="button">
            Batal
          </ButtonPrimary>
          <ButtonPrimary variant="contained" type="submit">
            Simpan
          </ButtonPrimary>
        </div>
      </form>
    </AppModal>
  )
}
