'use client'
import { twMerge } from 'tailwind-merge'
import { IIcons, SVGIcons } from '../icon'
import { Typography } from '../ui'
import { cn } from '@/lib/utils'

export type ButtonPrimaryInterface = {
  size?: 'xs' | 'sm' | 'base' | 'lg'
  onClick?: React.MouseEventHandler<HTMLButtonElement>
  onHover?: (props: boolean) => void | React.MouseEventHandler<HTMLButtonElement>
  color?: 'primary' | 'info' | 'danger' | 'warning' | 'success' | 'gray'
  className?: string
  classLabel?: string
  textSize?: string
  textColor?: string
  disabled?: boolean
  icon?: IIcons
  children: React.ReactNode
  type?: 'submit' | 'button'
  variant: 'contained' | 'outlined'
  isLoading?: boolean
}

function ButtonPrimaryV2({
  size = 'base',
  children,
  color,
  className,
  classLabel,
  textColor,
  textSize,
  disabled,
  icon,
  onClick,
  onHover,
  type,
  variant,
  isLoading,
}: ButtonPrimaryInterface) {
  function computedSize() {
    switch (size) {
      case 'xs':
        return 'px-3 py-2 '
      case 'sm':
        return 'px-4 py-2.5 '
      case 'base':
        return 'px-4 py-3.5 '
      case 'lg':
        return 'pl-9 pr-10 py-4 '
      default:
        return 'pl-9 pr-10 py-3 '
    }
  }

  function computedTextSize() {
    switch (size) {
      case 'xs':
        return cn('text-body-md', textSize)
      case 'sm':
        return cn('text-body-md', textSize)
      case 'base':
        return cn('text-body-lg', textSize)
      case 'lg':
        return cn('text-body-lg', textSize)
      default:
        return cn('text-body-lg', textSize)
    }
  }

  function computedColor() {
    if (variant === 'contained') {
      if (disabled || isLoading) return 'bg-gray-300 opacity-30 border-transparent cursor-not-allowed'
      if (color === 'primary')
        return 'bg-main-100 border-transparent hover:bg-main-200 active:bg-main-300 focus:outline-none focus:ring focus:ring-main-100 focus:ring-opacity-50'
      if (color === 'info')
        return 'bg-info-100 border-transparent hover:bg-info-200 active:bg-info-300 focus:outline-none focus:ring focus:ring-info-100 focus:ring-opacity-50'
      if (color === 'success')
        return 'bg-success-100 border-transparent hover:bg-success-200 active:bg-success-300 focus:outline-none focus:ring focus:ring-success-100 focus:ring-opacity-50'
      if (color === 'danger')
        return 'bg-danger-100 border-transparent hover:bg-danger-200 active:bg-danger-300 focus:outline-none focus:ring focus:ring-danger-100 focus:ring-opacity-50'
      if (color === 'warning')
        return 'bg-warning-100 border-transparent hover:bg-warning-200 active:bg-warning-300 focus:outline-none focus:ring focus:ring-warning-100 focus:ring-opacity-50'
      if (color === 'gray')
        return 'bg-gray-100 border-transparent hover:bg-gray-200 active:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-100 focus:ring-opacity-50'
      return 'bg-main-100 border-transparent hover:bg-main-200 active:bg-main-300 focus:outline-none focus:ring focus:ring-main-100 focus:ring-opacity-50'
    } else {
      if (disabled || isLoading) return 'bg-gray-300 border-gray-200 opacity-50 cursor-not-allowed'
      if (color === 'primary') return 'border-main-100 bg-white hover:bg-main-50 active:bg-main-100/10'
      if (color === 'info') return 'border-info-100 hover:border-info-200 active:bg-info-100/10'
      if (color === 'success') return 'border-success-100 hover:border-success-200 active:bg-success-100/10'
      if (color === 'danger')
        return 'border-line-200 hover:border-danger-200/60 active:bg-danger-100/10 focus:outline-none focus:ring focus:ring-danger-100 focus:ring-opacity-50'
      if (color === 'warning') return 'border-warning-100 hover:border-warning-200 active:bg-warning-100/10'
      if (color === 'gray')
        return 'border-line-200 hover:border-gray-100 active:bg-gray-100/10 focus:outline-none focus:ring focus:ring-gray-100 focus:ring-opacity-50'
      return 'border-main-100 bg-white hover:bg-main-50 active:bg-main-100/10'
    }
  }

  function computedTextColor() {
    if (variant === 'outlined') {
      if (disabled || isLoading) return cn('text-line-100 cursor-not-allowed', textColor)
      if (color === 'primary') return cn('text-main-100', textColor)
      if (color === 'info') return cn('text-info-100', textColor)
      if (color === 'success') return cn('text-success-100', textColor)
      if (color === 'danger') return cn('text-danger-100', textColor)
      if (color === 'warning') return cn('text-warning-100', textColor)
      if (color === 'gray') return cn('text-gray-400', textColor)
      return cn('text-main-100', textColor)
    } else {
      if (disabled || isLoading) return cn('text-line-100 cursor-not-allowed', textColor)
      return cn('text-white', textColor)
    }
  }

  const Spinner = () => {
    return (
      <div className="flex space-x-1 justify-center items-center">
        <span className="sr-only">Loading...</span>
        <div className="h-2 w-2 bg-line-100 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
        <div className="h-2 w-2 bg-line-100 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
        <div className="h-2 w-2 bg-line-100 rounded-full animate-bounce"></div>
        <span className="text-inherit pl-2">{children}</span>
      </div>
    )
  }

  return (
    <button
      disabled={disabled || isLoading}
      type={type}
      onClick={onClick}
      onMouseEnter={() => onHover && onHover(true)}
      onMouseLeave={() => onHover && onHover(false)}
      data-testid="button-primary-component"
      className={twMerge(
        `${computedSize()}${computedColor()} flex justify-center items-center rounded-2xl border ${className ?? ''}`
      )}
    >
      {!!icon && <SVGIcons name={icon} className="mr-2" />}
      <Typography className={`${computedTextSize()} ${computedTextColor()} font-bold ${classLabel}`}>
        {isLoading ? <Spinner /> : children}
      </Typography>
    </button>
  )
}

export default ButtonPrimaryV2
