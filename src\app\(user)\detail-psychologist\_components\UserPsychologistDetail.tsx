'use client'
import { IIcons, SVGIcons } from '@/components/_common/icon'
import { UserPsychologistDetailProps, useUserPsychologistDetails } from '../hook/useUserPsychologistDetail'
import { useGetPsychologistAvailabilityDate } from '../hook/useGetPsychologistAvailabilityDate'
import Image from 'next/image'
import PsychologistVideoCarousel from './section/VideoSection'
import { useSelector } from '@/store'
import { Button } from '@/components/ui/button'
import { H2 } from '@/components/_common/ui'
import BookingSection from './section/BookingSection'
import { useState, useEffect, useRef } from 'react'
import BookingModal from './section/BookingModal'
import { QuoteIcon } from '@radix-ui/react-icons'
import { toast } from '@/components/ui/use-toast'

function formatEducationYear(dateString: string): string {
  try {
    const graduationDate = new Date(dateString)
    if (isNaN(graduationDate.getTime())) return ''

    return graduationDate.getFullYear().toString()
  } catch (error) {
    return ''
  }
}

export default function UserPsychologistDetail({ psychologistId }: UserPsychologistDetailProps) {
  const { user } = useSelector((state) => state.Authentication)
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false)

  const { psychologist, loadingPsycho, errorPsycho } = useUserPsychologistDetails(psychologistId)
  const { availabilityDate, loadingDate, errorDate } = useGetPsychologistAvailabilityDate(psychologistId)

  // Handle date change from BookingSection (no longer needed for main data refetch)
  const handleDateChange = (date: string) => {
    // This callback is now only used by BookingSection internally for schedule updates
    console.log('Date changed to:', date)
  }

  // Handle share button click
  const handleShareClick = async () => {
    try {
      const profileUrl = `${window.location.origin}/detail-psychologist/${psychologistId}`
      await navigator.clipboard.writeText(profileUrl)

      toast({
        title: 'Berhasil',
        description: 'Link profil psikolog berhasil disalin',
        variant: 'success',
      })
    } catch (error) {
      console.error('Failed to copy link:', error)
      toast({
        title: 'Gagal',
        description: 'Gagal menyalin link profil',
        variant: 'danger',
      })
    }
  }

  // Extract problem categories from psychologist data
  const specializations = psychologist?.problemCategory?.map((item) => item.problemCategory) || []

  // Hide share button when user scrolls near bottom (to avoid overlapping bottom nav)
  const [hideShareOnScroll, setHideShareOnScroll] = useState(false)
  useEffect(() => {
    const onScroll = () => {
      const threshold = 800 // px from bottom where button will hide; tweak as needed
      const nearBottom = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - threshold
      // Only hide after the user has actually scrolled (avoid hiding on initial load)
      setHideShareOnScroll(nearBottom && window.scrollY > 0)
    }
    window.addEventListener('scroll', onScroll, { passive: true })
    return () => window.removeEventListener('scroll', onScroll)
  }, [])

  // Intro video fullscreen for mobile
  const [showIntroVideo, setShowIntroVideo] = useState(false)
  const introVideoRef = useRef<HTMLVideoElement | null>(null)

  useEffect(() => {
    const v = introVideoRef.current
    if (showIntroVideo) {
      if (v) {
        // try play and request fullscreen
        v.play().catch(() => {})
        const requestFS = (el: any) => {
          try {
            if (el.requestFullscreen) return el.requestFullscreen()
            if (el.webkitEnterFullScreen) return (el as any).webkitEnterFullScreen()
            if (el.webkitRequestFullscreen) return (el as any).webkitRequestFullscreen()
          } catch (e) {
            // ignore
          }
        }
        requestFS(v)
      }
    } else {
      // pause and exit fullscreen when overlay closed
      if (v) {
        try { v.pause() } catch (e) {}
      }
      try {
        if (document.fullscreenElement) document.exitFullscreen().catch(() => {})
        // webkit
        if ((document as any).webkitFullscreenElement) try { (document as any).webkitCancelFullScreen() } catch (e) {}
      } catch (e) {}
    }
    // cleanup when unmount
    return () => {
      if (v) try { v.pause() } catch (e) {}
    }
  }, [showIntroVideo])

  // Calculate years of experience and return as range
  const getExperienceYears = (): string => {
    if (!psychologist?.calculatedExperience) return '0-2 tahun'

    const years = Number(psychologist.calculatedExperience) || 0
    
    if (years < 2) {
      return '0-2 tahun'
    } else if (years >= 2 && years < 4) {
      return '2-4 tahun'
    } else if (years >= 4 && years <= 10) {
      return '4-10 tahun'
    } else {
      return '>10 tahun'
    }
  }

  if (loadingPsycho || loadingDate) {
    return (
      <div className="w-full max-w-4xl mx-auto p-4 flex justify-center items-center min-h-40">
        <div className="animate-pulse text-main-500">Loading...</div>
      </div>
    )
  }

  if (errorPsycho || !psychologist || errorDate) {
    return (
      <div className="w-full max-w-4xl mx-auto p-4 text-center text-red-500">
        Error loading psychologist data
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-col w-full max-w-5xl mx-auto px-3 md:px-6 mt-4 sm:mt-6 pb-20 md:pb-8">
        {/* Profile Header Section */}
        <div className="relative w-full">
          <div className="relative pb-4">
            {/* Mobile Banner Background - Only visible on mobile */}
            <div className="block sm:hidden absolute -top-9 left-0 right-0 h-48 overflow-hidden rounded-t-2xl">
              <Image
                src="/images/banner/bannerUserProfileMobile.svg"
                alt="Profile Banner"
                fill
                className="object-cover"
                priority
              />
            </div>

            {/* Mobile/Desktop Photo Layout */}
            <div className="flex flex-col sm:flex-row sm:items-start relative z-10">
              {/* Profile Photo - left aligned on all screens */}
              <div className="flex justify-start mb-4 sm:mb-0 sm:mr-6 sm:pl-4 relative">
                <div className="relative w-28 h-28 sm:w-32 sm:h-32 rounded-full overflow-hidden mt-12 sm:mt-0 border-4 border-white shadow-lg transform translate-y-[40%] sm:translate-y-0 ml-2 sm:ml-0">
                  {psychologist.profilePhoto ? (
                    <Image
                      width={128}
                      height={128}
                      src={psychologist.profilePhoto}
                      alt={psychologist.fullName}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-r from-main-100 to-purple-300"></div>
                  )}
                </div>

                {/* Mobile Video Introduction Button - Only visible if video exists and on mobile */}
                {psychologist.video && (
                  <button 
                    className="block sm:hidden absolute left-8 bottom-0 transform translate-y-[200%] z-20"
                    onClick={() => setShowIntroVideo(true)}
                  >
                    <svg width="68" height="26" viewBox="0 0 68 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="1" y="1" width="66" height="24" rx="8" fill="#039EE9" stroke="white" strokeWidth="2"/>
                      <path d="M17.3975 17.8973C17.2024 18.0274 17.004 18.0339 16.8024 17.9168C16.6008 17.7998 16.5 17.6242 16.5 17.39V8.60998C16.5 8.37585 16.6008 8.20025 16.8024 8.08318C17.004 7.96611 17.2024 7.97262 17.3975 8.10269L24.3045 12.5122C24.4866 12.6293 24.5776 12.7919 24.5776 13C24.5776 13.2081 24.4866 13.3707 24.3045 13.4878L17.3975 17.8973ZM17.6707 16.3364L22.9192 13L17.6707 9.66359V16.3364Z" fill="white"/>
                      <path d="M32.368 8.6C32.784 8.6 33.172 8.72 33.532 8.96C33.9 9.2 34.196 9.524 34.42 9.932C34.644 10.34 34.756 10.796 34.756 11.3C34.756 11.796 34.644 12.248 34.42 12.656C34.196 13.064 33.9 13.392 33.532 13.64C33.172 13.88 32.784 14 32.368 14H30.16L30.28 13.784V16.268C30.28 16.476 30.216 16.652 30.088 16.796C29.96 16.932 29.792 17 29.584 17C29.384 17 29.22 16.932 29.092 16.796C28.964 16.652 28.9 16.476 28.9 16.268V9.332C28.9 9.124 28.968 8.952 29.104 8.816C29.248 8.672 29.424 8.6 29.632 8.6H32.368ZM32.368 12.632C32.52 12.632 32.668 12.568 32.812 12.44C32.956 12.312 33.076 12.148 33.172 11.948C33.268 11.74 33.316 11.524 33.316 11.3C33.316 11.068 33.268 10.852 33.172 10.652C33.076 10.452 32.956 10.292 32.812 10.172C32.668 10.044 32.52 9.98 32.368 9.98H30.136L30.28 9.764V12.824L30.148 12.632H32.368ZM37.5705 16.268C37.5705 16.476 37.4985 16.652 37.3545 16.796C37.2185 16.932 37.0465 17 36.8385 17C36.6385 17 36.4705 16.932 36.3345 16.796C36.1985 16.652 36.1305 16.476 36.1305 16.268V8.852C36.1305 8.644 36.1985 8.472 36.3345 8.336C36.4785 8.192 36.6545 8.12 36.8625 8.12C37.0705 8.12 37.2385 8.192 37.3665 8.336C37.5025 8.472 37.5705 8.644 37.5705 8.852V16.268ZM44.5008 10.46C44.7088 10.46 44.8808 10.528 45.0168 10.664C45.1528 10.8 45.2208 10.976 45.2208 11.192V16.268C45.2208 16.476 45.1528 16.652 45.0168 16.796C44.8808 16.932 44.7088 17 44.5008 17C44.2928 17 44.1208 16.932 43.9848 16.796C43.8488 16.652 43.7808 16.476 43.7808 16.268V15.68L44.0448 15.788C44.0448 15.892 43.9888 16.02 43.8768 16.172C43.7648 16.316 43.6128 16.46 43.4208 16.604C43.2288 16.748 43.0008 16.872 42.7368 16.976C42.4808 17.072 42.2008 17.12 41.8968 17.12C41.3448 17.12 40.8448 16.98 40.3968 16.7C39.9488 16.412 39.5928 16.02 39.3288 15.524C39.0728 15.02 38.9448 14.444 38.9448 13.796C38.9448 13.14 39.0728 12.564 39.3288 12.068C39.5928 11.564 39.9448 11.172 40.3848 10.892C40.8248 10.604 41.3128 10.46 41.8488 10.46C42.1928 10.46 42.5088 10.512 42.7968 10.616C43.0848 10.72 43.3328 10.852 43.5408 11.012C43.7568 11.172 43.9208 11.336 44.0328 11.504C44.1528 11.664 44.2128 11.8 44.2128 11.912L43.7808 12.068V11.192C43.7808 10.984 43.8488 10.812 43.9848 10.676C44.1208 10.532 44.2928 10.46 44.5008 10.46ZM42.0768 15.8C42.4288 15.8 42.7368 15.712 43.0008 15.536C43.2648 15.36 43.4688 15.12 43.6128 14.816C43.7648 14.512 43.8408 14.172 43.8408 13.796C43.8408 13.412 43.7648 13.068 43.6128 12.764C43.4688 12.46 43.2648 12.22 43.0008 12.044C42.7368 11.868 42.4288 11.78 42.0768 11.78C41.7328 11.78 41.4288 11.868 41.1648 12.044C40.9008 12.22 40.6928 12.46 40.5408 12.764C40.3968 13.068 40.3248 13.412 40.3248 13.796C40.3248 14.172 40.3968 14.512 40.5408 14.816C40.6928 15.12 40.9008 15.36 41.1648 15.536C41.4288 15.712 41.7328 15.8 42.0768 15.8ZM51.544 10.58C51.752 10.58 51.924 10.652 52.06 10.796C52.196 10.932 52.264 11.104 52.264 11.312V16.412C52.264 17.148 52.124 17.732 51.844 18.164C51.572 18.604 51.204 18.92 50.74 19.112C50.276 19.304 49.756 19.4 49.18 19.4C48.924 19.4 48.652 19.38 48.364 19.34C48.076 19.3 47.84 19.24 47.656 19.16C47.416 19.056 47.248 18.924 47.152 18.764C47.064 18.612 47.048 18.448 47.104 18.272C47.176 18.04 47.292 17.88 47.452 17.792C47.612 17.712 47.78 17.704 47.956 17.768C48.084 17.808 48.252 17.868 48.46 17.948C48.668 18.036 48.908 18.08 49.18 18.08C49.548 18.08 49.852 18.028 50.092 17.924C50.34 17.828 50.524 17.66 50.644 17.42C50.772 17.188 50.836 16.868 50.836 16.46V15.656L51.088 15.944C50.952 16.208 50.78 16.428 50.572 16.604C50.372 16.772 50.132 16.9 49.852 16.988C49.572 17.076 49.252 17.120 48.892 17.120C48.468 17.120 48.096 17.024 47.776 16.832C47.464 16.632 47.22 16.36 47.044 16.016C46.868 15.664 46.78 15.264 46.78 14.816V11.312C46.78 11.104 46.848 10.932 46.984 10.796C47.12 10.652 47.292 10.58 47.5 10.58C47.708 10.58 47.88 10.652 48.016 10.796C48.152 10.932 48.22 11.104 48.22 11.312V14.384C48.22 14.904 48.332 15.272 48.556 15.488C48.788 15.696 49.108 15.8 49.516 15.8C49.796 15.8 50.032 15.748 50.224 15.644C50.416 15.532 50.564 15.372 50.668 15.164C50.772 14.948 50.824 14.688 50.824 14.384V11.312C50.824 11.104 50.892 10.932 51.028 10.796C51.164 10.652 51.336 10.58 51.544 10.58Z" fill="white"/>
                    </svg>
                  </button>
                )}
              </div>

              {/* Text Content - left aligned on all screens, with mobile spacing adjustment */}
              <div className="sm:flex-1 text-left mt-6 sm:mt-0">
                {/* Name */}
                <div className="mt-4 sm:mt-0">
                  <div className="flex items-center justify-start gap-2 flex-wrap">
                    <h1 className="text-xl sm:text-2xl font-bold break-words">{psychologist.fullName}</h1>
                  </div>
                  <p className="text-gray-400 mt-1 break-words">
                    {psychologist.field && psychologist.field[0]?.name
                      ? `Psikolog ${psychologist.field[0].name}`
                      : 'Psikolog Klinis'}
                  </p>
                </div>

                {/* Rating and experience */}
                <div className="flex items-center justify-start gap-3 mt-3 flex-wrap">
                  <div className="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                    <svg
                      className="w-4 h-4 text-yellow-400 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="ml-1 text-sm font-medium">{psychologist.overallRating || 5.0}</span>
                    <span className="text-gray-500 ml-1 text-sm">({psychologist.testimonyCount} ulasan)</span>
                  </div>
                  <div className="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                    <SVGIcons name={IIcons.SuitCase} />
                    <span className="ml-1 text-sm font-medium">{getExperienceYears()}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Share button - remains at top right; hide when near bottom to avoid overlapping bottom nav */}
            <div
              className={`absolute top-[10rem] sm:top-4 right-4 z-20 transition-opacity duration-200 ${
                hideShareOnScroll ? 'opacity-0 pointer-events-none' : 'opacity-100'
              }`}
            >
              <Button
                onClick={handleShareClick}
                className="w-10 h-10 rounded-full flex items-center justify-center p-0"
                variant="outline"
                size="icon"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 30 30"
                  className="text-gray-800"
                >
                  <path d="M 23 3 A 4 4 0 0 0 19 7 A 4 4 0 0 0 19.09375 7.8359375 L 10.011719 12.376953 A 4 4 0 0 0 7 11 A 4 4 0 0 0 3 15 A 4 4 0 0 0 7 19 A 4 4 0 0 0 10.013672 17.625 L 19.089844 22.164062 A 4 4 0 0 0 19 23 A 4 4 0 0 0 23 27 A 4 4 0 0 0 27 23 A 4 4 0 0 0 23 19 A 4 4 0 0 0 19.986328 20.375 L 10.910156 15.835938 A 4 4 0 0 0 11 15 A 4 4 0 0 0 10.90625 14.166016 L 19.988281 9.625 A 4 4 0 0 0 23 11 A 4 4 0 0 0 27 7 A 4 4 0 0 0 23 3 z"></path>
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Specialization Section - Mobile: after header, Desktop: hidden */}
        <div className="block lg:hidden mt-4">
          <h2 className="text-base sm:text-lg font-medium mb-2">Spesialisasi</h2>

          <div className="flex flex-wrap gap-2">
            {specializations.length > 0 ? (
              specializations.map((spec) => (
                <span key={spec} className="px-3 py-1.5 text-xs text-gray-700 border rounded-full">
                  {spec}
                </span>
              ))
            ) : (
              <p className="text-xs text-gray-500">Tidak ada spesialisasi yang ditampilkan</p>
            )}
          </div>
        </div>

        {/* Main Content with Responsive Ordering */}
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Desktop Booking Section */}
          <div className="hidden lg:block w-full lg:w-1/3 order-first lg:order-last mb-4 lg:mb-0">
            <div className="sticky top-4">
              <BookingSection
                user={user}
                psychologist={psychologist}
                availabilityDate={availabilityDate}
                onDateChange={handleDateChange}
              />
            </div>
          </div>

          {/* Left Column Content - For desktop, makes up 2/3 of width */}
          <div className="w-full lg:w-2/3 order-last lg:order-first flex flex-col">
            {/* Video Section - Desktop only */}
            <div className="hidden lg:block w-full mb-4">
              <div className="relative w-full rounded-lg overflow-hidden">
                {/* Aspect ratio container */}
                <div className="w-full relative pb-[60%] sm:pb-[56.25%]">
                  <div className="absolute inset-0">
                    <PsychologistVideoCarousel
                      video={psychologist.video}
                      youtubeVideos={psychologist.youtubeVideo}
                      profilePhoto={psychologist.profilePhoto}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm text-gray-600">5 Minute Mindfulness Meditation</span>
                <a href="#" className="text-main-500 text-sm font-medium">
                  Lihat semua
                </a>
              </div>
            </div>

            {/* Specialization Section - Desktop only */}
            <div className="hidden lg:block mt-2 mb-4">
              <H2 className="text-base sm:text-lg font-medium mb-3">Spesialisasi</H2>
              <div className="flex flex-wrap gap-2">
                {specializations.length > 0 ? (
                  specializations.map((spec) => (
                    <span key={spec} className="px-3 py-1.5 text-xs text-gray-700 border rounded-full">
                      {spec}
                    </span>
                  ))
                ) : (
                  <p className="text-xs sm:text-sm text-gray-500">Tidak ada spesialisasi yang ditampilkan</p>
                )}
              </div>
            </div>

            {/* Education Section */}
            <div className="mt-4 sm:mt-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">Pendidikan</h2>
              {psychologist.educationHistory && psychologist.educationHistory.length > 0 ? (
                psychologist.educationHistory.map((education, index) => (
                  <div key={index} className="flex items-start gap-2 sm:gap-3 mb-2 sm:mb-3">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 flex-shrink-0 rounded-xl overflow-hidden flex items-center justify-center">
                      <Image
                        src="/logo/education/education.png"
                        alt="Icon pendidikan"
                        width={40}
                        height={40}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="min-w-0 flex-grow">
                      <h3 className="font-medium text-xs sm:text-sm truncate">{education.major}</h3>
                      <p className="text-xs text-gray-600 truncate">
                        {education.level} {education.university}
                      </p>
                      <p className="text-xs text-gray-500">
                        {education.graduationYear && formatEducationYear(education.graduationYear)}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-xs sm:text-sm text-gray-500">Tidak ada informasi pendidikan</p>
              )}
            </div>

            {/* About Section */}
            <div className="mt-4 sm:mt-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">Tentang Psikolog</h2>
              <p className="text-xs sm:text-sm text-gray-700 break-words leading-relaxed">
                {psychologist.bio || 'Tidak ada informasi tentang psikolog ini.'}
              </p>
            </div>

            {/* License Section */}
            <div className="mt-4 sm:mt-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">Surat Izin Praktik Psikolog</h2>
              <p className="text-xs sm:text-sm text-gray-700 break-words">
                {psychologist.sipp || 'Tidak tersedia'}
              </p>
            </div>

            {/* Video Section - Mobile: before testimonials, Desktop: hidden */}
            <div className="block lg:hidden mt-4 sm:mt-6">
              <div className="relative w-full rounded-lg overflow-hidden">
                {/* Aspect ratio container */}
                <div className="w-full relative pb-[60%] sm:pb-[56.25%]">
                  <div className="absolute inset-0">
                    <PsychologistVideoCarousel
                      video={psychologist.video}
                      youtubeVideos={psychologist.youtubeVideo}
                      profilePhoto={psychologist.profilePhoto}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm text-gray-600">5 Minute Mindfulness Meditation</span>
                <a href="#" className="text-main-500 text-sm font-medium">
                  Lihat semua
                </a>
              </div>
            </div>

            {/* Testimonials Section */}
            <div className="mt-4 sm:mt-6 mb-6">
              <h2 className="text-base sm:text-lg font-medium mb-2">
                Testimoni {psychologist.testimonyCount > 0 && `(${psychologist.testimonyCount})`}
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {psychologist.testimonyCount > 0 ? (
                  [1, 2, 3].map((i) => (
                    <div key={i} className="p-2 sm:p-3 border border-gray-200 rounded-lg">
                      <div className="text-main-500 text-lg mb-1">
                        <QuoteIcon className="text-main-100" />
                      </div>
                      <p className="text-xs sm:text-sm text-gray-700 mb-1 sm:mb-2 line-clamp-3">
                        Konseling dengan {psychologist.nickname || psychologist.fullName.split(',')[0]} sangat
                        membantu dalam mengatasi masalah yang saya hadapi.
                      </p>
                      <p className="text-xs text-gray-600 mb-1 sm:mb-2 line-clamp-2">
                        Psikolog sangat profesional dan memberikan perspektif baru yang membantu saya melihat
                        masalah dari sudut pandang berbeda.
                      </p>
                      <a href="#" className="text-main-500 text-xs">
                        Baca selengkapnya
                      </a>
                      <div className="flex flex-wrap items-center mt-2 sm:mt-3 gap-2">
                        <p className="text-xs text-gray-700">Klien, {i % 2 === 0 ? 'Wanita' : 'Pria'}</p>
                        <div className="flex items-center">
                          <span className="text-yellow-400 mr-1">★</span>
                          <span className="text-xs">{Math.min(5, psychologist.overallRating || 5)}</span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-xs sm:text-sm text-gray-500 col-span-full">Belum ada testimoni</p>
                )}
              </div>
              {psychologist.testimonyCount > 3 && (
                <button className="mt-2 sm:mt-3 text-main-500 text-xs sm:text-sm font-medium">
                  Lihat Lebih Banyak
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white p-4 lg:hidden z-50 shadow-2xl">
        <Button
          onClick={() => setIsBookingModalOpen(true)}
          className="w-full bg-main-100 hover:bg-main-200 text-white py-3 rounded-lg font-semibold text-base"
        >
          Cek Jadwal
        </Button>
      </div>

      {/* Booking Modal for Mobile */}
      <BookingModal
        isOpen={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
        user={user}
        psychologist={psychologist}
        availabilityDate={availabilityDate}
        onDateChange={handleDateChange}
      />
      {/* Intro video overlay for mobile */}
      {showIntroVideo && psychologist.video && (
        <div className="fixed inset-0 z-[60] bg-black/90 flex items-center justify-center">
          <button
            className="absolute top-4 right-4 text-white z-70"
            onClick={() => setShowIntroVideo(false)}
            aria-label="Close video"
          >
            ✕
          </button>
          <video
            ref={introVideoRef}
            className="w-full h-full object-contain max-h-full"
            src={psychologist.video}
            controls
            playsInline
          />
        </div>
      )}
    </>
  )
}
