import { AppBigCaption, AppBigText, AppMediumText, Card } from '@/components/_common/ui'
import WorkIcon from '@/assets/icons/work.svg'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'

import { SpecializationList } from '@/components/home/<USER>/OurPsychologist/SpecializationList'
import { PsychologistProps } from '@/components/home/<USER>/OurPsychologist/PsychologistCardItem'
import { ScheduleList, formatSchedule } from '@/components/home/<USER>/OurPsychologist/ScheduleList'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'

export const MobileCardItem = (psychologist: PsychologistProps) => {
  const specialization = psychologist?.problemCategory?.length
    ? psychologist?.problemCategory.map((item) => item.problemCategory)
    : []

  // Function to convert experience years to range
  const getExperienceRange = (experience: string | number) => {
    const years = Number(experience) || 0

    if (years < 2) {
      return '0-2 tahun'
    } else if (years >= 2 && years < 4) {
      return '2-4 tahun'
    } else if (years >= 4 && years <= 10) {
      return '4-10 tahun'
    } else {
      return '>10 tahun'
    }
  }

  // Check if there are actual available schedules
  const hasAvailableSchedules =
    psychologist.breakdownAvailability &&
    psychologist.breakdownAvailability.length > 0 &&
    formatSchedule(psychologist.breakdownAvailability, 20).length > 0
  return (
    <Card className="p-3 xs:p-3 sm:p-3 md:p-3 bg-white flex flex-col justify-center gap-y-2 w-full z-1">
      <Link href={`/detail-psychologist/${psychologist?.id}`} className="flex items-start gap-x-4">
        <div className="flex-shrink-0">
          <AvatarWithInfo className="w-[72px] h-[72px]" image={psychologist?.profilePhoto ?? ''} />
        </div>
        <div className="flex-1 min-w-0">
          {/* Nama - Fixed height untuk konsistensi */}
          <div className="h-[24px] overflow-hidden mb-2">
            <AppBigText bold className="line-clamp-1 leading-6">
              {psychologist?.fullName}
            </AppBigText>
          </div>

          <AppBigCaption className="text-gray-200 grid gap-y-2">
            {/* Spesialisasi - Fixed height untuk konsistensi */}
            <div className="h-[32px] overflow-hidden">
              <SpecializationList
                className="text-caption-md font-medium text-gray-300 line-clamp-2"
                list={specialization}
                showItem={2}
              />
            </div>
            <span className="flex items-center gap-x-1 text-gray-400 text-body-sm font-bold">
              <WorkIcon />
              {getExperienceRange(psychologist?.calculatedExperience)}
            </span>
          </AppBigCaption>
        </div>
      </Link>

      {/* Show schedule if available */}
      {hasAvailableSchedules && (
        <>
          <div className="-mx-3">
            <Separator orientation="horizontal" />
          </div>
          <div className="-mx-3 px-3">
            <ScheduleList
              breakdownAvailability={psychologist.breakdownAvailability}
              maxItems={20}
              psychologistId={psychologist.id}
            />
          </div>
        </>
      )}
    </Card>
  )
}
