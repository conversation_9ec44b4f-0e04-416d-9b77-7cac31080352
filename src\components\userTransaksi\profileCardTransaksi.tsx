import Avatar from '@/components/navbar/Avatar'

export default function ProfileCardTransaksi({
  nama,
  job,
  flexDirection,
  photo,
}: {
  nama: string
  photo: string
  job: string
  flexDirection: string
}) {
  return (
    <>
      <div className="flex items-center gap-2 mt-3 md:mt-0">
        <figure className="border-2 border-white rounded-full w-[64px] h-[64px] flex justify-center items-center">
          <Avatar height={60} width={60} image={photo} />
        </figure>
        <figcaption className={`flex ${flexDirection} gap-[2px]`}>
          <span className="text-[#222222] text-[16px]">{nama}</span>
          {/* <span className="text-[#222222] text-[16px]">{job}</span> */}
        </figcaption>
      </div>
    </>
  )
}
