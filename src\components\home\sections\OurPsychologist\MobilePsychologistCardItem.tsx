import { AppBigCaption, AppBigText, Card } from '@/components/_common/ui'
import { SpecializationList } from './SpecializationList'
import WorkIcon from '@/assets/icons/work.svg'
import { AvatarWithInfo } from '@/components/_common/CardInfo/AvatarWithInfo'
import { PsychologistProps } from './PsychologistCardItem'
import { Separator } from '@/components/ui/separator'
import { ScheduleList } from './ScheduleList'
import Link from 'next/link'

export const MobilePsychologistCardItem = (psychologist: PsychologistProps) => {
  const specialization = psychologist?.problemCategory?.length
    ? psychologist?.problemCategory.map((item) => item?.problemCategory)
    : []

  return (
    <Card className="p-3 xs:p-3 sm:p-3 md:p-3 bg-white flex md:hidden flex-col justify-center gap-y-2 w-full z-1">
      <div className="flex items-start gap-x-4">
        <div className="flex-shrink-0">
          <AvatarWithInfo className="w-[72px] h-[72px]" image={psychologist?.profilePhoto ?? ''} />
        </div>
        <div className="flex-1 min-w-0">
          {/* Nama - Fixed height untuk konsistensi */}
          <div className="h-[24px] overflow-hidden mb-2">
            <Link href={`/detail-psychologist/${psychologist?.id}`}>
              <AppBigText bold className="line-clamp-1 leading-6">
                {psychologist?.fullName}
              </AppBigText>
            </Link>
          </div>

          <AppBigCaption className="text-gray-200 grid gap-y-2">
            {/* Spesialisasi - Fixed height untuk konsistensi */}
            <div className="h-[32px] overflow-hidden">
              <SpecializationList
                className="text-caption-md font-medium text-gray-300 line-clamp-2"
                list={specialization}
                showItem={2}
              />
            </div>
            <span className="flex items-center gap-x-1 text-gray-400 text-body-sm font-bold">
              <WorkIcon />
              {Number(psychologist?.calculatedExperience) < 1
                ? 'Kurang dari 1'
                : psychologist?.calculatedExperience || '0'}{' '}
              tahun
            </span>
          </AppBigCaption>
        </div>
      </div>

      {/* Show schedule if available */}
      {psychologist.breakdownAvailability && psychologist.breakdownAvailability.length > 0 && (
        <>
          <div className="-mx-3">
            <Separator orientation="horizontal" />
          </div>
          <div className="-mx-3 px-3">
            <ScheduleList
              breakdownAvailability={psychologist.breakdownAvailability}
              maxItems={20}
              psychologistId={psychologist.id}
            />
          </div>
        </>
      )}
    </Card>
  )
}
