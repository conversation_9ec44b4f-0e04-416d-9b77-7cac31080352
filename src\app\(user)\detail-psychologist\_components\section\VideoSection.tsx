'use client'
import { useState, useEffect, useRef } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Pagination, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import { Button } from '@/components/ui/button'

interface YoutubeVideo {
  title?: string
  link: string
  description?: string
}

interface VideoSource {
  type: 'youtube' | 'video' | 'placeholder'
  url?: string
  title?: string
  subtitle?: string
  thumbnail?: string
  duration?: string
  isPlaying?: boolean
  orientation?: 'landscape' | 'portrait'
}

interface PsychologistVideoCarouselProps {
  video?: string
  youtubeVideos?: YoutubeVideo[]
  profilePhoto?: string
}

export default function PsychologistVideoCarousel({
  video,
  youtubeVideos,
  profilePhoto,
}: PsychologistVideoCarouselProps) {
  const [videoSources, setVideoSources] = useState<VideoSource[]>([])
  const [activeVideo, setActiveVideo] = useState<number | null>(null)
  const [isFullscreenPortrait, setIsFullscreenPortrait] = useState(false)
  const [isVideoFullscreen, setIsVideoFullscreen] = useState(false)
  const videoRefs = useRef<HTMLVideoElement[]>([])
  const swiperRef = useRef<any>(null)
  const [activeIndex, setActiveIndex] = useState(0)
  const videoContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const sources: VideoSource[] = []

    // Add direct video if available
    if (video) {
      sources.push({
        type: 'video',
        url: video,
        title: '5 Minute Mindfulness Meditation',
        subtitle: 'Great Meditation',
        thumbnail: profilePhoto,
        duration: '5:15',
        isPlaying: false,
        orientation: 'landscape', // Default, will be checked when loaded
      })
    }

    // Add YouTube videos if available
    if (youtubeVideos && youtubeVideos.length > 0) {
      youtubeVideos.forEach((ytVideo) => {
        const title = ytVideo.title && ytVideo.title !== ytVideo.link ? ytVideo.title : 'YouTube Video'
        const subtitle = ytVideo.description || 'Something Video'

        sources.push({
          type: 'youtube',
          url: ytVideo.link,
          title: title,
          subtitle: subtitle,
          duration: '3:45',
          isPlaying: false,
          orientation: 'landscape', // Default, will be detected when thumbnail loads
        })
      })
    }

    // Only add placeholder if we want to show something when no videos are available
    // For now, we'll leave this empty to hide the section when no videos are available
    
    setVideoSources(sources)
  }, [video, youtubeVideos, profilePhoto])

  // Function to extract YouTube ID
  const extractYoutubeId = (url: string): string => {
    if (!url) return ''

    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
    const match = url.match(regExp)

    return match && match[2].length === 11 ? match[2] : ''
  }

  // Get YouTube thumbnail URL
  const getYoutubeThumbnail = (url: string): string => {
    const videoId = extractYoutubeId(url)
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
  }

  const handlePlay = (index: number) => {
    // Pause all other videos first
    videoRefs.current.forEach((video, i) => {
      if (video && i !== index) {
        video.pause()
      }
    })

    setActiveVideo(index)

    // Update playing state
    const updatedSources = videoSources.map((source, i) => ({
      ...source,
      isPlaying: i === index,
    }))

    setVideoSources(updatedSources)
  }

  const handleVideoPause = (index: number) => {
    const updatedSources = videoSources.map((source, i) => ({
      ...source,
      isPlaying: i === index ? false : source.isPlaying,
    }))

    setVideoSources(updatedSources)
    setActiveVideo(null)
  }

  const handleVideoPlay = (index: number) => {
    const updatedSources = videoSources.map((source, i) => ({
      ...source,
      isPlaying: i === index ? true : source.isPlaying,
    }))

    setVideoSources(updatedSources)
    setActiveVideo(index)
  }

  const goToPrev = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev()
    }
  }

  const goToNext = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext()
    }
  }

  const handleSlideChange = () => {
    if (swiperRef.current?.swiper) {
      setActiveIndex(swiperRef.current.swiper.activeIndex)
      setIsFullscreenPortrait(false) // Reset fullscreen mode when changing slides
    }
  }

  // Function to detect image orientation
  const detectOrientation = (width: number, height: number): 'landscape' | 'portrait' => {
    return width >= height ? 'landscape' : 'portrait'
  }

  // Handler for image load to detect orientation
  const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>, index: number) => {
    const img = event.target as HTMLImageElement
    const orientation = detectOrientation(img.naturalWidth, img.naturalHeight)

    if (orientation !== videoSources[index].orientation) {
      const updatedSources = [...videoSources]
      updatedSources[index] = {
        ...updatedSources[index],
        orientation: orientation,
      }
      setVideoSources(updatedSources)
    }
  }

  // Toggle fullscreen for portrait videos
  const toggleFullscreenPortrait = () => {
    setIsFullscreenPortrait(!isFullscreenPortrait)
  }

  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsVideoFullscreen(
        !!(
          document.fullscreenElement ||
          (document as any).webkitFullscreenElement ||
          (document as any).mozFullScreenElement ||
          (document as any).msFullscreenElement
        )
      )
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    }
  }, [])

  return (
    <>
      {/* Only render the video section if there are actual videos available */}
      {videoSources.length > 0 && (
        <div className="w-full overflow-hidden">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-2 gap-2">
            <h2 className="text-base font-medium">Video dari psikolog</h2>
            <div className="hidden sm:flex gap-x-2">
              <button
                onClick={goToPrev}
                disabled={videoSources.length <= 1 || activeIndex === 0}
                className={`w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md ${
                  videoSources.length <= 1 || activeIndex === 0 ? 'opacity-30 cursor-not-allowed' : ''
                }`}
                aria-label="Previous video"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  className="w-5 h-5"
                >
                  <path stroke="#737373" d="m15 6-6 6 6 6"></path>
                </svg>
              </button>

              {videoSources.length > 1 && (
                <button
                  onClick={goToNext}
                  disabled={activeIndex === videoSources.length - 1}
                  className={`w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md ${
                    activeIndex === videoSources.length - 1 ? 'opacity-30 cursor-not-allowed' : ''
                  }`}
                  aria-label="Next video"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    fill="none"
                    className="w-5 h-5"
                  >
                    <path stroke="#737373" d="m9 6 6 6-6 6"></path>
                  </svg>
                </button>
              )}
            </div>
          </div>

      <div className="w-full relative">
        {/* Mobile navigation buttons - outside the swiper container */}
        {videoSources.length > 1 && (
          <>
            <button
              onClick={goToPrev}
              disabled={activeIndex === 0}
              className={`absolute left-1 top-1/2 -translate-y-1/2 z-30 w-8 h-8 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center transition-opacity sm:hidden ${
                activeIndex === 0 ? 'opacity-30 cursor-not-allowed' : ''
              }`}
              aria-label="Previous slide"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" className="text-white">
                <path stroke="currentColor" strokeWidth="2" d="m12 4-6 6 6 6"></path>
              </svg>
            </button>

            <button
              onClick={goToNext}
              disabled={activeIndex === videoSources.length - 1}
              className={`absolute right-1 top-1/2 -translate-y-1/2 z-30 w-8 h-8 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center transition-opacity sm:hidden ${
                activeIndex === videoSources.length - 1 ? 'opacity-30 cursor-not-allowed' : ''
              }`}
              aria-label="Next slide"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" className="text-white">
                <path stroke="currentColor" strokeWidth="2" d="m6 4 6 6-6 6"></path>
              </svg>
            </button>
          </>
        )}

        {/* Desktop navigation buttons - hidden on mobile */}
        <button
          onClick={goToPrev}
          disabled={videoSources.length <= 1 || activeIndex === 0}
          className={`hidden sm:block absolute left-2 top-1/2 -translate-y-1/2 z-20 w-10 h-10 bg-white bg-opacity-70 hover:bg-opacity-90 rounded-full items-center justify-center shadow-md transition-all ${
            videoSources.length <= 1 || activeIndex === 0 ? 'opacity-0' : 'flex'
          }`}
          aria-label="Previous slide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" className="w-5 h-5">
            <path stroke="#737373" strokeWidth="2" d="m15 6-6 6 6 6"></path>
          </svg>
        </button>

        <button
          onClick={goToNext}
          disabled={videoSources.length <= 1 || activeIndex === videoSources.length - 1}
          className={`hidden sm:block absolute right-2 top-1/2 -translate-y-1/2 z-20 w-10 h-10 bg-white bg-opacity-70 hover:bg-opacity-90 rounded-full items-center justify-center shadow-md transition-all ${
            videoSources.length <= 1 || activeIndex === videoSources.length - 1 ? 'opacity-0' : 'flex'
          }`}
          aria-label="Next slide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" className="w-5 h-5">
            <path stroke="#737373" strokeWidth="2" d="m9 6 6 6-6 6"></path>
          </svg>
        </button>

        <Swiper
          ref={swiperRef}
          modules={[Pagination, Navigation]}
          spaceBetween={0}
          slidesPerView={1}
          className="rounded-lg overflow-hidden shadow-sm"
          onSlideChange={handleSlideChange}
          navigation={false}
          allowTouchMove={true}
          touchRatio={1}
          touchAngle={45}
        >
          {videoSources.map((source, index) => (
            <SwiperSlide key={index}>
              <div 
                ref={videoContainerRef}
                className={`relative w-full h-auto rounded-lg overflow-hidden bg-gray-100 ${
                  isVideoFullscreen ? 'fixed inset-0 z-50 rounded-none bg-black' : ''
                }`}
              >
                {/* Container with adaptive aspect ratio based on orientation */}
                <div
                  className={`relative ${
                    isVideoFullscreen 
                      ? 'w-full h-full flex items-center justify-center bg-black'
                      : source.orientation === 'portrait' && !isFullscreenPortrait
                        ? 'aspect-video max-w-lg mx-auto bg-black' // Constrained width for portrait videos in normal mode
                        : source.orientation === 'portrait' && isFullscreenPortrait
                          ? 'aspect-[9/16] max-w-md mx-auto bg-black' // Full height for portrait videos in fullscreen mode
                          : 'aspect-video w-full' // Default for landscape videos
                  }`}
                >
                  {/* Portrait mode toggle button */}
                  {source.orientation === 'portrait' && source.isPlaying && (
                    <button
                      onClick={toggleFullscreenPortrait}
                      className="absolute top-2 right-2 z-10 bg-black bg-opacity-50 text-white p-1 rounded-full"
                      aria-label={isFullscreenPortrait ? 'Minimize video' : 'Expand video'}
                    >
                      {isFullscreenPortrait ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"
                          />
                        </svg>
                      )}
                    </button>
                  )}

                  {source.type === 'video' && !source.isPlaying && (
                    <div className="relative w-full h-full">
                      <div className="w-full h-full bg-gradient-to-r from-purple-400 via-main-300 to-purple-500 opacity-50 absolute"></div>
                      {source.thumbnail ? (
                        <img
                          src={source.thumbnail}
                          alt={source.title || 'Video thumbnail'}
                          className={`w-full h-full ${source.orientation === 'portrait' ? 'object-contain' : 'object-cover'}`}
                          loading="lazy"
                          onLoad={(e) => handleImageLoad(e, index)}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.onerror = null
                            target.src = '/api/placeholder/1200/675'
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-300"></div>
                      )}
                      <div
                        className="absolute inset-0 flex items-center justify-center cursor-pointer"
                        onClick={() => handlePlay(index)}
                      >
                        <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 bg-white bg-opacity-30 rounded-full flex items-center justify-center">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center">
                            <div className="w-0 h-0 border-t-4 sm:border-t-6 border-t-transparent border-l-6 sm:border-l-8 border-l-gray-800 border-b-4 sm:border-b-6 border-b-transparent ml-1"></div>
                          </div>
                        </div>
                      </div>
                      <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 text-white max-w-[80%]">
                        <h3 className="text-sm sm:text-base md:text-lg font-medium truncate">
                          {source.title}
                        </h3>
                        <p className="text-xs sm:text-sm opacity-80 truncate">{source.subtitle}</p>
                      </div>
                      {source.duration && (
                        <div className="absolute bottom-2 sm:bottom-4 right-2 sm:right-4 bg-black bg-opacity-70 text-white px-1 sm:px-2 py-0.5 sm:py-1 rounded text-xs">
                          {source.duration}
                        </div>
                      )}
                    </div>
                  )}

                  {source.type === 'video' && source.isPlaying && (
                    <div className={`w-full h-full ${source.orientation === 'portrait' ? 'bg-black' : ''} ${
                      isVideoFullscreen ? 'flex items-center justify-center' : ''
                    }`}>
                      <video
                        ref={(el) => {
                          if (el) videoRefs.current[index] = el
                        }}
                        className={`object-contain bg-black ${
                          isVideoFullscreen 
                            ? 'max-w-full max-h-full w-auto h-auto' 
                            : 'w-full h-full'
                        }`}
                        controls
                        autoPlay
                        playsInline
                        preload="metadata"
                        controlsList="nodownload"
                        onPlay={() => handleVideoPlay(index)}
                        onPause={() => handleVideoPause(index)}
                        onEnded={() => handleVideoPause(index)}
                        onLoadedMetadata={(e) => {
                          const video = e.target as HTMLVideoElement
                          const orientation = detectOrientation(video.videoWidth, video.videoHeight)
                          const updatedSources = [...videoSources]
                          updatedSources[index] = {
                            ...updatedSources[index],
                            orientation: orientation,
                          }
                          setVideoSources(updatedSources)
                        }}
                        style={{
                          maxWidth: isVideoFullscreen ? '100vw' : '100%',
                          maxHeight: isVideoFullscreen ? '100vh' : '100%',
                        }}
                      >
                        <source src={source.url} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                  )}

                  {source.type === 'youtube' && !source.isPlaying && (
                    <div className="relative w-full h-full">
                      <img
                        src={getYoutubeThumbnail(source.url || '')}
                        alt={source.title || 'YouTube thumbnail'}
                        className={`w-full h-full ${source.orientation === 'portrait' ? 'object-contain' : 'object-cover'}`}
                        loading="lazy"
                        onLoad={(e) => handleImageLoad(e, index)}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement
                          target.onerror = null
                          target.src = '/api/placeholder/1200/675'
                        }}
                      />
                      <div
                        className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center cursor-pointer"
                        onClick={() => handlePlay(index)}
                      >
                        <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 bg-white bg-opacity-30 rounded-full flex items-center justify-center">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center">
                            <div className="w-0 h-0 border-t-4 sm:border-t-6 border-t-transparent border-l-6 sm:border-l-8 border-l-gray-800 border-b-4 sm:border-b-6 border-b-transparent ml-1"></div>
                          </div>
                        </div>
                      </div>
                      <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 text-white max-w-[80%]">
                        <h3 className="text-sm sm:text-base md:text-lg font-medium truncate">
                          {source.title}
                        </h3>
                        <p className="text-xs sm:text-sm opacity-80 truncate">{source.subtitle}</p>
                      </div>
                      {source.duration && (
                        <div className="absolute bottom-2 sm:bottom-4 right-2 sm:right-4 bg-black bg-opacity-70 text-white px-1 sm:px-2 py-0.5 sm:py-1 rounded text-xs">
                          {source.duration}
                        </div>
                      )}
                    </div>
                  )}

                  {source.type === 'youtube' && source.isPlaying && (
                    <div className={`w-full h-full ${source.orientation === 'portrait' ? 'bg-black' : ''}`}>
                      <iframe
                        src={`https://www.youtube.com/embed/${extractYoutubeId(source.url || '')}?autoplay=1`}
                        title={source.title || 'YouTube video'}
                        frameBorder="0"
                        className="w-full h-full"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      ></iframe>
                    </div>
                  )}

                  {source.type === 'placeholder' && (
                    <div className="relative w-full h-full bg-purple-200 rounded-lg overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-400 via-main-300 to-purple-500 opacity-80"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 bg-white bg-opacity-30 rounded-full flex items-center justify-center">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-12 md:h-12 bg-white rounded-full flex items-center justify-center">
                            <div className="w-0 h-0 border-t-4 sm:border-t-6 border-t-transparent border-l-6 sm:border-l-8 border-l-gray-800 border-b-4 sm:border-b-6 border-b-transparent ml-1"></div>
                          </div>
                        </div>
                      </div>
                      <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 text-white">
                        <h3 className="text-sm sm:text-base md:text-lg font-medium">Video tidak tersedia</h3>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Mobile pagination dots */}
        {videoSources.length > 1 && (
          <div className="flex justify-center gap-2 mt-3 sm:hidden">
            {videoSources.map((_, index) => (
              <button
                key={index}
                onClick={() => swiperRef.current?.swiper?.slideTo(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  activeIndex === index ? 'bg-main-100' : 'bg-gray-300'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
      )}
    </>
  )
}
